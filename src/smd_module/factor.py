import math
import pathlib
import shelve
from functools import lru_cache
from typing import Dict, List, Mapping, Optional, Sequence

import numpy as np
import pandas as pd
from shared_utils.label import SymbolConverter

import aichemy.database_manager as adm
from aichemy.database_manager import del_factor, del_key, from_key, to_key  # noqa: F401
from aichemy.utils import acquire_flock, is_notebook, to_nstimestamp

from .utils import (
    get_930open_df,
    get_all_securities_,
    get_stock_related_industries,
    query_ashare_outstanding_share_table,
    query_liquid_market_cap_table,
    query_market_cap_table,
)

if is_notebook():
    from tqdm.notebook import tqdm
else:
    from tqdm import tqdm
try:
    from mindgo_api import (  # type: ignore
        get_all_securities,
        get_all_trade_days,
        get_money_flow_step,
        get_open_api,
        get_price,
        get_trade_days,
        query,
        run_query,
    )
except ImportError:
    pass

HXFACTOR = [
    "量价强化学习类",
    "情绪强化学习类",
    "集合竞价量价类",
    "Tick快照量价类",
    "分钟频量价技术指标类",
    "日频量价技术指标类",
    "Tick快照深度模型类",
    "Transformer量价预测类",
    "新闻预测文本训练类",
    "逐笔成交频次量价类",
    "逐笔委托频次量价类",
]
OTHER_FACTOR = ["市值因子", "行业因子", "行业量价因子", "资金流", "财务因子", "行情", "风险暴露"]
TEST_HXFACTOR = ["000300.SH", "000852.SH", "000905.SH"]
EXCEPTED_FACTOR = {
    "量价强化学习类": ["RL_DAY_MF_ALPHA6", "RL_DAY_MF_ALPHA11", "RL_DAY_MF_ALPHA7"],
    "日频量价技术指标类": ["DAY_PV_VVR"],
    "集合竞价量价类": ["OB_BT_NBOC", "SS_RD_CA1R"],
    "新闻预测文本训练类": ["NEWS_BUZZ_IWF", "NEWS_BUZZ_ICQF"],
}

DEFAULT_FACTOR_CACHE_FILE = "/home/<USER>/work/share/simu/yangfan/factors.stock.h5"
DEFAULT_INDUSTRY_DICT_CACHE_FILE = "/home/<USER>/work/share/simu/yangfan/industry_dict.db"


@lru_cache
def get_hxfactor_dict() -> Dict[str, List[str]]:
    """从数据库中获取因子字典，如{'量价强化学习类': ['RL_DAY_MF_ALPHA6', 'RL_DAY_MF_ALPHA11', 'RL_DAY_MF_ALPHA7']}"""
    factor_dict = {}
    open_api = get_open_api("share:research")
    for i in HXFACTOR:
        data = open_api.get_hxfactors_data(i, "now", "now", fields=None)
        factor_dict[i] = data.columns.difference(["time", "code"]).tolist()
    test = open_api.get_hxfactors_test()
    for i in TEST_HXFACTOR:
        factor_dict[i] = test.query(f'基准指数=="{i}"')["因子名称"].tolist()
    return factor_dict


def get_factor_dict(
    factor_cache_file: str = DEFAULT_FACTOR_CACHE_FILE, database: str = "raw"
) -> Mapping[str, Sequence[str]]:
    """获取目前已经保存到本地的因子字典

    Args:
        factor_cache_file (str, optional): 因子缓存文件. Defaults to DEFAULT_FACTOR_CACHE_FILE.
        database (str, optional): 数据库名称. Defaults to "raw".

    Returns:
        Dict[str, List[str]]: {'量价强化学习类': ['RL_DAY_MF_ALPHA6', 'RL_DAY_MF_ALPHA11', 'RL_DAY_MF_ALPHA7']}
    """
    return adm.get_factor_dict(factor_cache_file, database)


def align_datetime_index(index: pd.DatetimeIndex) -> pd.DatetimeIndex:
    """要求index已经normalize"""
    # if not index.difference(gl_all_trade_days).empty:
    #     raise RuntimeError(f"因子数据中包含非交易日数据，日期为：{index.difference(gl_all_trade_days)}")
    gl_all_trade_days = get_all_trade_days()
    return gl_all_trade_days[(gl_all_trade_days >= index.min()) & (gl_all_trade_days <= index.max())].sort_values()


def dump_price(start_dt: str, end_dt: str, factor_cache_file: str = DEFAULT_FACTOR_CACHE_FILE):
    fields = [
        "open",
        "close",
        "low",
        "high",
        "volume",
        "turnover",
        "turnover_rate",
        "high_limit",
        "low_limit",
        "avg_price",
        "is_paused",
    ]
    tmp = get_price(
        get_all_securities_(start_dt, end_dt),
        start_dt,
        end_dt,
        "1d",
        fields,
        fq="post",
        skip_paused=True,
        is_panel=True,
    )
    for field in fields:
        data = tmp[field].reindex(index=align_datetime_index(tmp[field].index))
        data.index = (pd.to_datetime(data.index) + pd.Timedelta(hours=7)).astype(int)
        save_data_to_store(to_key("raw", "行情", field), data, store_file=factor_cache_file)

    open930 = get_930open_df(start_dt, end_dt)
    open930 = open930.reindex(index=align_datetime_index(open930.index))
    open930.index = (pd.to_datetime(open930.index) + pd.Timedelta(hours=7)).astype(int)
    save_data_to_store(to_key("raw", "行情", "930open"), open930, store_file=factor_cache_file)


def dump_benchmark(
    benchmarks: List[str], start_dt: str, end_dt: str, factor_cache_file: str = DEFAULT_FACTOR_CACHE_FILE
):
    fields = ["open", "close", "low", "high", "volume", "turnover"]
    all_symbols = get_all_securities_(start_dt, end_dt)
    tmp = get_price(benchmarks, start_dt, end_dt, "1d", fields, fq="post", skip_paused=True)
    for benchmark in benchmarks:
        for field in fields:
            data = tmp[benchmark][field].reindex(index=align_datetime_index(tmp[benchmark][field].index))
            data.index = (pd.to_datetime(data.index) + pd.Timedelta(hours=7)).astype(int)
            data = pd.DataFrame(
                data.to_numpy().reshape(-1, 1).repeat(len(all_symbols), axis=1), index=data.index, columns=all_symbols
            )
            save_data_to_store(
                to_key("raw", "benchmark", f"{SymbolConverter.qs_from_mindgo(benchmark)}_{field}"),
                data,
                store_file=factor_cache_file,
            )


def dump_hxfactor(
    name1: str,
    name2: Optional[List],
    save_mode: int,
    start_dt: str,
    end_dt: str,
    chunk_size: int = 50,
    factor_cache_file: str = DEFAULT_FACTOR_CACHE_FILE,
) -> None:
    """保存因子

    Args:
        name1 (str): 因子大类
        name2 (Optional[List]): 因子名称,None表示获取所有
        save_mode (int): 0 - 跳过已经保存的因子，只保存新的因子，1 - 所有因子全都重新保存，2 - 已经保存的因子做增量更新
        start_dt (str): 开始日期
        end_dt (str): 结束日期
        chunk_size (int, optional): 分块大小. Defaults to 10.
        factor_cache_file (str, optional): 因子缓存文件. Defaults to DEFAULT_FACTOR_CACHE_FILE.
    """
    all_hxf0 = get_hxfactor_dict().get(name1, [])  # 获取数据库里所有因子的列表
    all_hxf1 = get_factor_dict(factor_cache_file).get(name1, [])  # 获取本地缓存的因子列表

    if save_mode == 0:
        if name2 is None:
            hxf = [i for i in all_hxf0 if i not in all_hxf1]
        else:
            hxf = [i for i in name2 if i not in all_hxf1]
    elif save_mode == 1:
        if name2 is None:
            hxf = all_hxf0
        else:
            hxf = name2
    elif save_mode == 2:
        if name2 is None:
            hxf = all_hxf1
        else:
            hxf = [i for i in name2 if i in all_hxf1]

    n = math.ceil(len(hxf) / chunk_size)
    # 分块来取数据，不然数据量很大
    for i in tqdm(range(n), desc=f"{name1}"):
        # 从数据库中取数据
        open_api = get_open_api("share:research")
        if name1 in TEST_HXFACTOR:
            data = open_api.get_hxfactors_test(start_dt, end_dt, fields=hxf[i::n])
        else:
            data = open_api.get_hxfactors_data(name1, start_dt, end_dt, fields=hxf[i::n])
        data.set_index(["time", "code"], drop=True, append=False, inplace=True)
        columns = data.columns.to_list()
        for factor_name in columns:
            d: pd.DataFrame = data[factor_name].copy().unstack()
            d.index.rename(None, inplace=True)
            d.columns.rename(None, inplace=True)

            d.index = pd.to_datetime(d.index)
            if name1 == "集合竞价量价类":
                d = d.reindex(index=align_datetime_index(d.index))
                d.index = (d.index + pd.Timedelta(hours=1, minutes=25)).astype(int)
            else:
                gl_all_trade_days = get_all_trade_days()
                d.index = gl_all_trade_days[
                    np.searchsorted(gl_all_trade_days.to_numpy(), d.index.to_numpy(), "left") - 1
                ]
                d = d.reindex(index=align_datetime_index(d.index))
                d.index = (d.index + pd.Timedelta(hours=7)).astype(int)

            key = to_key("raw", name1, factor_name)
            if save_mode == 0 or save_mode == 1:
                save_data_to_store(key, d.drop(["920002.SZ"], axis=1, errors="ignore"), factor_cache_file, mode="w")
            else:
                save_data_to_store(key, d.drop(["920002.SZ"], axis=1, errors="ignore"), factor_cache_file, mode="a")


def dump_market_cap_factor(start_dt: str, end_dt: str, factor_cache_file: str = DEFAULT_FACTOR_CACHE_FILE) -> None:
    def fnc(mct_: pd.DataFrame, start_dt: str, end_dt: str):
        mct = mct_.copy()
        mct.index = pd.to_datetime(mct.index)
        mct = mct.reindex(index=align_datetime_index(mct.index))
        mct.index = (mct.index + pd.Timedelta(hours=7)).astype(int)
        st = to_nstimestamp(f"{start_dt} 000000")
        ed = to_nstimestamp(f"{end_dt} 150000")
        mct = mct.loc[(mct.index >= st) & (mct.index <= ed)]
        return mct

    # 市值因子是包含A股、B股、港股等的总市值
    # 流通市值因子是包含A股、B股等的流通市值
    for func, name in [(query_market_cap_table, "市值因子"), (query_liquid_market_cap_table, "流通市值因子")]:
        mct = func()
        mct = fnc(mct, start_dt, end_dt)
        save_data_to_store(to_key("raw", "市值因子", name), mct, factor_cache_file)

    mct = query_ashare_outstanding_share_table()
    mct = fnc(mct, start_dt, end_dt)
    close_df = get_price(mct.columns.to_list(), start_dt, end_dt, "1d", ["close"], fq=None, is_panel=True)["close"]
    close_df.index = (pd.to_datetime(close_df.index) + pd.Timedelta(hours=7)).astype(int)
    save_data_to_store(to_key("raw", "市值因子", "A股流通市值"), close_df * mct, factor_cache_file)


def dump_money_flow_factor(start_dt: str, end_dt: str, factor_cache_file: str = DEFAULT_FACTOR_CACHE_FILE) -> None:
    fields = [
        "act_buy_xl",
        "pas_buy_xl",
        "act_buy_l",
        "pas_buy_l",
        "act_buy_m",
        "pas_buy_m",
        "act_sell_xl",
        "pas_sell_xl",
        "act_sell_l",
        "pas_sell_l",
        "act_sell_m",
        "pas_sell_m",
        "buy_l",
        "sell_l",
        "dde_l",
        "net_flow_rate",
        "l_net_value",
    ]
    data = get_money_flow_step(
        get_all_securities_(start_dt, end_dt),
        start_date=start_dt,
        end_date=end_dt,
        fre_step="1d",
        fields=fields,
        is_panel=True,
    )
    for i in fields:
        j = data[i].copy()
        j.index = pd.to_datetime(j.index)
        j = j.reindex(index=align_datetime_index(j.index))
        j.index = (j.index + pd.Timedelta(hours=7)).astype(int)
        save_data_to_store(to_key("raw", "资金流", i), j, factor_cache_file)


def dump_industry_factor(
    start_dt: str,
    end_dt: str,
    factor_cache_file: str = DEFAULT_FACTOR_CACHE_FILE,
    industry_dict_cache_file: str = DEFAULT_INDUSTRY_DICT_CACHE_FILE,
) -> None:
    """保存行业因子

    Args:
        start_dt (str): 开始日期
        end_dt (str): 结束日期
        factor_cache_file (str, optional): 因子缓存文件. Defaults to DEFAULT_FACTOR_CACHE_FILE.
        industry_dict_cache_file (str, optional): 行业字典缓存文件. Defaults to DEFAULT_INDUSTRY_DICT_CACHE_FILE.
    """
    res = []
    for td in get_trade_days(start_dt, end_dt):  # type: ignore
        industry_dict = _get_industry_dict(td, industry_dict_cache_file)
        res.append(pd.DataFrame(industry_dict).loc["同花顺二级行业"].rename(td))
    res1: pd.DataFrame = pd.concat(res, axis=1).T  # index为日期，columns为股票代码，values为industry_index_thscode
    # index为nstimestamp，columns为股票代码
    res1.index = pd.to_datetime(res1.index)
    res1 = res1.reindex(index=align_datetime_index(res1.index))
    res1.index = (res1.index + pd.Timedelta(hours=7)).astype(int)

    fields = ["open", "close", "high", "low", "volume", "turnover"]
    res3 = {field: pd.DataFrame(np.nan, index=res1.index, columns=res1.columns) for field in fields}

    for industry_index_thscode in [i for i in set(res1.values.flatten()) if isinstance(i, str)]:
        dddd = res1 == industry_index_thscode
        res2 = dddd.copy().astype(float)
        save_data_to_store(to_key("raw", "行业因子", industry_index_thscode), res2, factor_cache_file)

        d = get_price(industry_index_thscode, start_dt, end_dt, "1d", fields, skip_paused=True, fq="post")
        d.index = (pd.to_datetime(d.index) + pd.Timedelta(hours=7)).astype(int)  # index为nstimestamp，columns为字段

        for field in fields:
            idx, col = d.index, res3[field].columns
            tmp = pd.DataFrame(d[field].to_numpy().reshape(-1, 1).repeat(len(col), axis=1), index=idx, columns=col)
            tmp = tmp.reindex(index=res3[field].index, fill_value=np.nan)
            res3[field][dddd] = tmp[dddd]

    for field in fields:
        save_data_to_store(to_key("raw", "行业量价因子", field), res3[field], factor_cache_file)


def dump_financial_factor(start_dt: str, end_dt: str, factor_cache_file: str = DEFAULT_FACTOR_CACHE_FILE) -> None:
    from mindgo_api import factor as smd_factor  # type: ignore

    cols = """pe
pe_ttm
pb
pcf_cash_flow_ttm
ps
ps_ttm
dividend_rate
dividend_rate_12_months
market_cap
capitalization
current_market_cap
circulating_cap
weighted_roe
overall_assets_net_income_ratio
roa
net_profit_margin_on_sales
gross_sales_profits
ratio_of_sales_to_cost
net_profit_div_income
opt_profit_div_income
before_tax_profit_div_income
opt_cost_div_income
sale_cost_div_income
administration_cost_div_income
financing_cost_div_income
impairment_loss_div_income
basic_pey_ear_growth_ratio
diluted_peg_rowth_ratio
net_cashflow_psg_rowth_ratio
overall_income_growth_ratio
opt_income_growth_ratio
opt_profit_growth_ratio
total_profit_growth_ratio
net_profit_growth_ratio
parent_company_profit_growth_ratio
parent_company_share_holders_net_profit_growth_ratio
net_cashflow_from_opt_act_growth_ratio
diluted_net_asset_growth_ratio
cash_cycle
days_sales_of_inventory
days_sales_outstanding
days_payable_outstanding
turnover_days_of_current_assets
inventory_turnover_ratio
turnover_ratio_of_receivable
turnover_ratio_of_account_payable
turnover_of_current_assets
turnover_of_fixed_assets
turnover_of_overall_assets
current_ratio
quick_ratio
conservative_quick_ratio
equity_ratio
equity_liabilities_attributable_to_shareholders_of_parent_company
equity_int_liabilities_attributable_to_shareholders_of_parent_company
tangible_assets_liabilities
tangible_assets_int_liabilities
tangible_assets_net_liabilities
ebitda_liabilities
netcashflows_from_opt_act_int_liabilities
netcashflows_from_opt_act_net_liabilities
long_term_debt_to_opt_capital_ratio
net_debt_equity
int_debt_equity""".split("\n")

    for i in tqdm(range(5), desc="财务因子"):
        aaa = cols[i::5]
        data = run_query(
            query(
                smd_factor.symbol, smd_factor.date, *[getattr(smd_factor, j) for j in aaa if hasattr(smd_factor, j)]
            ).filter(smd_factor.date.between(f"{start_dt}", f"{end_dt}"))
        )
        data.columns = data.columns.str.replace(r"^factor_", "", regex=True)
        data.drop(["id"], axis=1, inplace=True, errors="ignore")
        data = data.set_index(["date", "symbol"])
        data.index.rename((None, None), inplace=True)
        for col in data.columns:
            tmp = data[col].copy().unstack()
            tmp.index = pd.to_datetime(tmp.index)
            tmp = tmp.reindex(index=align_datetime_index(tmp.index))
            tmp.index = (tmp.index + pd.Timedelta(hours=7)).astype(int)
            save_data_to_store(to_key("raw", "财务因子", col), tmp, factor_cache_file)


def save_data_to_store(key: str, data: pd.DataFrame, store_file: str = DEFAULT_FACTOR_CACHE_FILE, mode: str = "a"):
    data = data.copy()
    data.columns = data.columns.map(SymbolConverter.qs_from_mindgo)
    adm.save_data_to_store(key, data, store_file, mode)


def _get_industry_dict(dt, industry_dict_cache_file: str) -> Dict[str, Dict[str, str]]:
    """返回指定日期每个股票的行业字典，如{'000995.SZ': {'同花顺二级行业': '881133.TI', '申万二级行业': '801125.SL', '中信二级行业': 'CI005156.CI'}}"""
    # 2021年7月30日，同花顺二级行业分类调整，旧的行业代码映射不到指数代码，这里需要手动做映射
    old_industry_code_dict = {
        "T0203": "881107.TI",
        "T0602": "881116.TI",
        "T0704": "881120.TI",
        "T1001": "881129.TI",
        "T1902": "881156.TI",
        "T1903": "881157.TI",
        "T2201": "881162.TI",
        "T2202": "881163.TI",
    }
    dt = pd.Timestamp(dt).strftime("%Y-%m-%d")
    industry_dict_cache_path = pathlib.Path(industry_dict_cache_file)
    industry_dict_cache_path.parent.mkdir(parents=True, exist_ok=True)

    industry_dict = {}
    flag = False
    with acquire_flock(industry_dict_cache_path.with_suffix(".lock"), "SH"):
        with shelve.open(industry_dict_cache_file) as db:
            # industry_dict - {'股票代码': {'同花顺二级行业': '881133.TI', '申万二级行业': '801125.SL', '中信二级行业': 'CI005156.CI'}}
            if dt in db:
                industry_dict = db[dt]
                flag = True

    if not flag:
        for symbol in tqdm(get_all_securities("stock", dt).index.tolist(), desc=dt):
            try:
                industry_dict[symbol] = dict(
                    [(i[1], i[0] if i[0] is not None else i[3]) for i in get_stock_related_industries(symbol, dt)]
                )
            except Exception:
                print(f"{dt} {symbol} 没有获取到相关行业")
        with acquire_flock(industry_dict_cache_path.with_suffix(".lock"), "EX"):
            with shelve.open(industry_dict_cache_file) as db:
                db[dt] = industry_dict

    for k in industry_dict.keys():
        if "同花顺二级行业" not in industry_dict[k]:
            print("{} {} 未获取到同花顺二级行业代码".format(dt, k))
            continue
        if industry_dict[k]["同花顺二级行业"] in old_industry_code_dict:
            industry_dict[k]["同花顺二级行业"] = old_industry_code_dict[industry_dict[k]["同花顺二级行业"]]
    return industry_dict


def get_factor(
    name1: str,
    name2: str,
    shift: int = 0,
    factor_cache_file: str = DEFAULT_FACTOR_CACHE_FILE,
    start_dt: Optional[str] = None,
    end_dt: Optional[str] = None,
    cached: bool = False,
    database: str = "raw",
) -> pd.DataFrame:
    """取因子数据

    Args:
        name1 (str): 因子大类名称
        name2 (str): 因子名称

        factor_cache_file (str, optional): 因子缓存文件. Defaults to DEFAULT_FACTOR_CACHE_FILE.
        start_dt (Optional[str], optional): 开始日期，None则取到最早，为None时如果因子数据不存在则报错. Defaults to None.
        end_dt (Optional[str], optional): 结束日期，None则取到最后，为None时如果因子数据不存在则报错. Defaults to None.

    Raises:
        ValueError: 当前缓存数据中没有所需的因子，需要传入start_dt 和 end_dt 用于提取数据

    Returns:
        pd.DataFrame: 因子数据
    """
    ret = adm.get_factor(
        name1,
        name2,
        database=database,
        factor_cache_file=factor_cache_file,
        start_dt=start_dt,
        end_dt=end_dt,
        shift=shift,
    )
    ret.columns = ret.columns.map(SymbolConverter.qs_to_mindgo)
    return ret
