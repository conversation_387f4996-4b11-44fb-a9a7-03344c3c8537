import datetime
import fcntl
import pathlib
import secrets
import socket
import string
import sys
from contextlib import contextmanager
from functools import lru_cache
from typing import Generator, Literal, Optional, Tuple, Union

import numba
import numpy as np
import numpy.typing as npt
import pandas as pd


def is_notebook():
    if "ipykernel_launcher.py" in sys.argv[0]:
        return True
    return False


def flatten_list(lst):
    """把嵌套的list展开"""
    flattened = []
    for item in lst:
        if isinstance(item, (tuple, list)):
            flattened.extend(flatten_list(item))
        else:
            flattened.append(item)
    return tuple(flattened)


def get_intersection(*sets):
    # 确保至少有一个集合作为参数
    if len(sets) < 1:
        return set()
    # 使用第一个集合初始化交集
    intersection = set(sets[0])
    # 遍历剩余的集合，更新交集
    for s in sets[1:]:
        intersection = intersection.intersection(s)
    return list(intersection)


def get_union(*sets):
    ret = set()
    for s in sets:
        ret = ret.union(s)
    return list(ret)


@lru_cache
def get_raw_daily_data(symbol, start_dt, end_dt):
    import qnt_research.api as qnt

    data = qnt.get_price(
        symbol,
        pd.Timestamp(start_dt).strftime("%Y-%m-%d"),
        pd.Timestamp(end_dt).strftime("%Y-%m-%d"),
        "1d",
        ["date", "time", "open", "high", "low", "close", "volume", "open_interest"],
    )
    futures_info = qnt.get_contract_info(symbol)
    data["turnover"] = data["close"] * data["volume"] * futures_info["contract_multiplier"]
    data["holding_amount"] = data["close"] * data["open_interest"] * futures_info["contract_multiplier"]
    data["trade_day"] = data["date"]
    return data


@lru_cache
def get_raw_minute_data(symbol, start_dt, end_dt):
    import qnt_research.api as qnt

    data = qnt.get_price(
        symbol,
        pd.Timestamp(start_dt).strftime("%Y-%m-%d %H:%M:%S"),
        pd.Timestamp(end_dt).strftime("%Y-%m-%d %H:%M:%S"),
        "1m",
        ["date", "time", "open", "high", "low", "close", "volume", "open_interest"],
    )
    futures_info = qnt.get_contract_info(symbol)
    data["turnover"] = data["close"] * data["volume"] * futures_info["contract_multiplier"]
    data["holding_amount"] = data["close"] * data["open_interest"] * futures_info["contract_multiplier"]
    data.loc[data["time"] == 150000000, "trade_day"] = data.loc[data["time"] == 150000000, "date"]
    data["trade_day"].bfill(inplace=True)
    data["trade_day"] = data["trade_day"].astype("int")
    return data


def get_merge_minute_data(data: pd.DataFrame, n: int) -> pd.DataFrame:
    """将一天数据按成交量切分成n个bar

    Args:
        data (pd.DataFrame): 原始数据
        n (int): 切分数量

    Returns:
        pd.DataFrame: 合并数据
    """
    ret = []

    lastday = 0
    today_vol = 0
    lastday_vol = 0
    start_merge = False

    current_volume = 0
    current_turnover = 0
    open = 0
    high = 0
    low = 0
    new_bar = True

    for _, i in data.iterrows():
        if lastday > 0 and i["trade_day"] != lastday:
            lastday_vol = today_vol
            today_vol = 0
            start_merge = True
        today_vol += i["volume"]
        lastday = i["trade_day"]

        if start_merge:
            # 更新字段
            if new_bar:
                open = i["open"]
                high = i["high"]
                low = i["low"]
                current_volume = 0
                current_turnover = 0
                new_bar = False
            else:
                high = max(high, i["high"])
                low = min(low, i["low"])
            current_volume += i["volume"]
            current_turnover += i["turnover"]

            # 是否完成当前bar
            if current_volume >= lastday_vol / n or i["time"] == 150000000:
                ret.append(
                    {
                        "date": i["date"],
                        "time": i["time"],
                        "open": open,
                        "high": high,
                        "low": low,
                        "close": i["close"],
                        "volume": current_volume,
                        "open_interest": i["open_interest"],
                        "turnover": current_turnover,
                        "holding_amount": i["holding_amount"],
                        "trade_day": i["trade_day"],
                    }
                )
                new_bar = True
    return pd.DataFrame(ret)


def convert_seconds_to_hms(seconds: float) -> str:
    """将以秒为单位的耗时统计转换为以时分秒来表示

    Args:
        seconds (float): 以秒为单位的耗时统计

    Returns:
        str: 以时分秒来表示的耗时统计
    """
    hours = seconds // 3600
    minutes = (seconds % 3600) // 60
    seconds = seconds % 60
    return f"{hours:.0f} h {minutes:.0f} m {seconds:.3f} s"


def to_nstimestamp(date_time: Union[str, pd.Timestamp, datetime.datetime], tz="Asia/Shanghai") -> int:
    """如果timestamp没有时区信息，则加上时区信息，再转换"""
    if isinstance(date_time, str):
        return int(pd.Timestamp(date_time, tz=tz).timestamp() * 1e9)
    else:
        if isinstance(date_time, datetime.datetime):
            date_time = pd.Timestamp(date_time)
        if date_time.tz is None:
            return int(date_time.tz_localize(tz).timestamp() * 1e9)
        else:
            return int(date_time.timestamp() * 1e9)


def from_nstimestamp(nstimestamp: Union[int, pd.Index], tz="Asia/Shanghai") -> Union[pd.Timestamp, pd.Index]:
    """如果timestamp没有时区信息，则加上时区信息，再转换"""
    return pd.to_datetime(nstimestamp, utc=True).tz_convert(tz)


def view(df: pd.DataFrame):
    from IPython.display import display

    res = df.copy()
    res.index = res.index.map(from_nstimestamp)
    display(res)


def rolling_window(
    start_dt: str,
    end_dt: str,
    forward_length: Union[pd.Timedelta, str],
    backward_length: Union[pd.Timedelta, str],
    tz: str = "Asia/Shanghai",
) -> Generator[Tuple[pd.Timestamp, pd.Timestamp, pd.Timestamp], None, None]:
    """获取滚动的时间窗口

    Args:
        start_dt (str): 数据开始时间
        end_dt (str): 数据结束时间
        forward_length (Union[pd.Timedelta, str]): 参与训练的数据长度
        backward_length (Union[pd.Timedelta, str]): apply的数据长度
        tz (str, optional): 时区. Defaults to "Asia/Shanghai".

    Yields:
        Generator[Tuple[pd.Timestamp, pd.Timestamp, pd.Timestamp], None, None]: 训练数据起点，训练数据终点，apply数据终点

    Examples:
        >>> def def train_one_round(t1, t2, t3):
        ...     print(t2)
        ...     train_data = {k: data_ops.slice_dataset(v, t1, t2, "both") for k, v in data.items()}
        ...     apply_data = {k: data_ops.slice_dataset(v, t2 - pd.Timedelta("60D"), t3, "right") for k, v in data.items()}
        >>> for t1, t2, t3 in data_ops.rolling_window("20200101", "20240331", "1100D", "60D"):
        ...     train_one_round(t1, t2, t3)
    """
    start_dt_ = pd.Timestamp(start_dt, tz=tz)
    end_dt_ = pd.Timestamp(end_dt, tz=tz)
    forward_length = pd.Timedelta(forward_length)
    backward_length = pd.Timedelta(backward_length)
    tmp = pd.date_range(end_dt_ - backward_length, start_dt_, freq=-backward_length, tz=tz).sort_values()
    tmp = tmp[tmp > (start_dt_ + forward_length)]
    for i in tmp:
        yield i - forward_length, i, i + backward_length


def slice_dataset(
    data: pd.DataFrame,
    start_dt: Optional[str] = None,
    end_dt: Optional[str] = None,
    include: Literal["both", "left", "right"] = "both",
    tz: str = "Asia/Shanghai",
) -> pd.DataFrame:
    """分割数据集

    Args:
        data (pd.DataFrame): 原始数据集
        start_dt (Optional[str], optional): 开始日期，None则取到最早. Defaults to None.
        end_dt (Optional[str], optional): 结束日期，None则取到最后. Defaults to None.
        include (Literal["both", "left", "right"], optional): 边界是否包含在内. Defaults to "both".

    Raises:
        ValueError: 边界参数错误

    Returns:
        pd.DataFrame: 分割后的数据集df
    """
    if data.empty:
        return data
    if start_dt is None and end_dt is None:
        return data
    sts = to_nstimestamp(start_dt if start_dt is not None else pd.Timestamp.min, tz=tz)
    ets = to_nstimestamp(end_dt if end_dt is not None else pd.Timestamp.max, tz=tz)
    if include == "both":
        return data.loc[(data.index >= sts) & (data.index <= ets)]
    elif include == "left":
        return data.loc[(data.index >= sts) & (data.index < ets)]
    elif include == "right":
        return data.loc[(data.index > sts) & (data.index <= ets)]
    else:
        raise ValueError(f"include must be 'both', 'left' or 'right', got {include}")


def rolling_data(train_len: int, pred_len: int, x: Tuple, y, *data) -> Generator[list, None, None]:
    """生成器，滚动读取数据集

    Args:
        train_len (int): 前面用于训练的数据长度
        pred_len (int): 后面用于预测的数据长度

    Yields:
        Generator[list, None, None]: 用于训练的数据1, 用于预测的数据1...
    """
    length = len(y)
    for i in [min(length, j) for j in range(train_len + pred_len, length + pred_len, pred_len)]:
        training_set_index = range(i - train_len - pred_len, i - pred_len)
        non_training_set_index = range(i - pred_len, i)
        yield [
            tuple(i[training_set_index] for i in x),
            tuple(i[non_training_set_index] for i in x),
            y[training_set_index],
            y[non_training_set_index],
            *[i[j] for i in data for j in [training_set_index, non_training_set_index]],
        ]


def generate_random_code(length=8):
    # 定义字符集
    characters = string.ascii_letters + string.digits  # 包含大写、小写和数字

    # 生成随机码
    random_code = "".join(secrets.choice(characters) for _ in range(length))

    return datetime.datetime.now().strftime("%Y%m%d%H%M%S-") + random_code


@numba.njit(numba.float64[:](numba.float64[:, :], numba.int64), cache=True)
def multi_csmean(array: npt.NDArray[np.float64], windows: int) -> npt.NDArray[np.float64]:
    """滚动计算均值，等价于rolling"""
    length = array.shape[0]
    ret = np.full(length, np.nan)
    for i in range(windows - 1, length):
        ret[i] = np.nanmean(array[i - windows + 1 : i + 1, :])
    return ret


def multi_csmean_for_df(df: pd.DataFrame, windows: int) -> pd.Series:
    return pd.Series(multi_csmean(df.to_numpy(), windows), index=df.index)


@numba.njit(numba.float64[:](numba.float64[:, :], numba.int64), cache=True)
def multi_csstd(array: npt.NDArray[np.float64], windows: int) -> npt.NDArray[np.float64]:
    """滚动计算标准差，等价于rolling"""
    length = array.shape[0]
    ret = np.full(length, np.nan)
    for i in range(windows - 1, length):
        ret[i] = np.nanstd(array[i - windows + 1 : i + 1, :])
    return ret


def multi_csstd_for_df(df: pd.DataFrame, windows: int) -> pd.Series:
    return pd.Series(multi_csstd(df.to_numpy(), windows), index=df.index)


@numba.njit(numba.float64[:](numba.float64[:, :], numba.int64, numba.float64), cache=True)
def multi_cspercentile(array: npt.NDArray[np.float64], windows: int, percentile: float) -> npt.NDArray[np.float64]:
    """滚动计算百分位数，等价于rolling"""
    length = array.shape[0]
    ret = np.full(length, np.nan)
    for i in range(windows - 1, length):
        ret[i] = np.nanpercentile(array[i - windows + 1 : i + 1, :], percentile)
    return ret


def multi_cspercentile_for_df(df: pd.DataFrame, windows: int, percentile: float) -> pd.Series:
    return pd.Series(multi_cspercentile(df.to_numpy(), windows, percentile), index=df.index)


@numba.njit(numba.float64[:](numba.float64[:, :], numba.int64), cache=True)
def multi_csmax(array: npt.NDArray[np.float64], windows: int) -> npt.NDArray[np.float64]:
    """滚动计算最大值，等价于rolling"""
    length = array.shape[0]
    ret = np.full(length, np.nan)
    for i in range(windows - 1, length):
        ret[i] = np.nanmax(array[i - windows + 1 : i + 1, :])
    return ret


def multi_csmax_for_df(df: pd.DataFrame, windows: int) -> pd.Series:
    return pd.Series(multi_csmax(df.to_numpy(), windows), index=df.index)


@numba.njit(numba.float64[:](numba.float64[:, :], numba.int64), cache=True)
def multi_csmin(array: npt.NDArray[np.float64], windows: int) -> npt.NDArray[np.float64]:
    """滚动计算最小值，等价于rolling"""
    length = array.shape[0]
    ret = np.full(length, np.nan)
    for i in range(windows - 1, length):
        ret[i] = np.nanmin(array[i - windows + 1 : i + 1, :])
    return ret


def multi_csmin_for_df(df: pd.DataFrame, windows: int) -> pd.Series:
    return pd.Series(multi_csmin(df.to_numpy(), windows), index=df.index)


@contextmanager
def acquire_flock(file, type_):
    """上下文管理器，用于获取和释放文件锁"""
    with open(file, "w") as f:
        # 获取独占锁
        fcntl.flock(f, {"EX": fcntl.LOCK_EX, "SH": fcntl.LOCK_SH}.get(type_, fcntl.LOCK_EX))
        try:
            yield f
        finally:
            # 释放锁
            fcntl.flock(f, fcntl.LOCK_UN)


class ResourceLimiter:
    def __init__(self, max_concurrent: int = 5, counter_file: str = "resource_counter.txt"):
        """初始化资源限制器

        Args:
            max_concurrent (int, optional): 同时允许的最大进程数，默认为5. Defaults to 5.
            counter_file (str, optional): 计数器文件路径. Defaults to "resource_counter.txt".
        """
        self.counter_file = counter_file
        self.max_concurrent = max_concurrent
        self._initialize_counter()

    @contextmanager
    def _acquire_lock(self):
        """上下文管理器，用于获取和释放文件锁"""
        with open(self.counter_file, "r+") as f:
            # 获取独占锁
            fcntl.flock(f, fcntl.LOCK_EX)
            try:
                yield f
            finally:
                # 释放锁
                fcntl.flock(f, fcntl.LOCK_UN)

    def _initialize_counter(self):
        """初始化计数器文件"""
        if self.max_concurrent <= 0:
            return
        if not pathlib.Path(self.counter_file).exists():
            with open(self.counter_file, "w") as f:
                f.write("0")

    def acquire(self):
        """获取资源"""
        if self.max_concurrent <= 0:
            return True
        with self._acquire_lock() as f:
            counter = int(f.read().strip())
            if counter < self.max_concurrent:
                f.seek(0)
                f.write(str(counter + 1))
                f.truncate()
                return True
            else:
                return False

    def release(self):
        """释放资源"""
        if self.max_concurrent <= 0:
            return
        with self._acquire_lock() as f:
            counter = int(f.read().strip())
            if counter > 0:
                f.seek(0)
                f.write(str(counter - 1))
                f.truncate()


def send_message(socket_path, message):
    # 创建 Unix Domain Socket
    sock = socket.socket(socket.AF_UNIX, socket.SOCK_STREAM)

    try:
        # 连接到服务端
        sock.connect(socket_path)
        sock.sendall(message.encode("utf-8"))
        print(f"客户端已发送消息: {message}")
    except FileNotFoundError:
        print("错误：未找到 socket 文件，服务端可能未启动。")
    except Exception as e:
        print(f"发送失败: {e}")
    finally:
        sock.close()


def wait_for_message(socket_path):
    if (tmp := pathlib.Path(socket_path)).exists():
        tmp.unlink()

    # 创建 Unix Domain Socket
    sock = socket.socket(socket.AF_UNIX, socket.SOCK_STREAM)

    # 绑定到文件路径
    sock.bind(socket_path)
    sock.listen(1)
    print(f"服务端监听中: {socket_path}")

    try:
        while True:
            conn, addr = sock.accept()  # addr 为空（Unix socket 没有地址信息）
            print("收到连接...")

            data = conn.recv(1024)
            if data:
                message = data.decode("utf-8")
                print(f"服务端收到消息: {message}, 触发执行任务...")
            conn.close()
            return

    except KeyboardInterrupt:
        print("\n服务端关闭。")
    finally:
        sock.close()
        # 清理 socket 文件
        if (tmp := pathlib.Path(socket_path)).exists():
            tmp.unlink()
