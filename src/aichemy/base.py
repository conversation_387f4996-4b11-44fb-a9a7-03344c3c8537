import datetime
import pickle
from abc import ABC, abstractmethod
from typing import ClassVar, List, Union

import pandas as pd

from .data_fetcher import DataFetcher


class FactorGen:
    _end_date: ClassVar[str] = ""
    _fq: ClassVar[str] = ""
    _count: ClassVar[int] = 0

    _open: ClassVar[pd.DataFrame] = pd.DataFrame()
    _close: ClassVar[pd.DataFrame] = pd.DataFrame()
    _high: ClassVar[pd.DataFrame] = pd.DataFrame()
    _low: ClassVar[pd.DataFrame] = pd.DataFrame()
    _volume: ClassVar[pd.DataFrame] = pd.DataFrame()
    _turnover: ClassVar[pd.DataFrame] = pd.DataFrame()
    _is_paused: ClassVar[pd.DataFrame] = pd.DataFrame()

    def __init__(self, data_fetcher: DataFetcher):
        self.data_fetcher = data_fetcher

    # 开盘价 最高价 最低价 收盘价 平均值 成交量 成交额 指数的开盘价 指数的收盘价
    # 返回的都是数据框
    def _fetch_price(self, code: Union[str, List[str]], end_date: str = "", fq: str = "forward", count: int = 350):
        code_: list[str] = [code] if isinstance(code, str) else code
        end_date = (pd.Timestamp(end_date) if end_date else datetime.date.today()).strftime("%Y-%m-%d")

        if end_date != self._end_date or fq != self._fq or count > self._count:
            FactorGen._end_date = end_date
            FactorGen._fq = fq
            FactorGen._count = max(500, count)

            tmp_code = list(self._open.columns.union(code_))
            temp = self.data_fetcher.get_price(tmp_code, self._end_date, "1d", self._fq, self._count)
            FactorGen._open = temp["open"]
            FactorGen._close = temp["close"]
            FactorGen._high = temp["high"]
            FactorGen._low = temp["low"]
            FactorGen._volume = temp["volume"]
            FactorGen._turnover = temp["turnover"]
            FactorGen._is_paused = temp["is_paused"]

        elif len(additions := list(set(code_).difference(self._open.columns))) > 0:
            temp = self.data_fetcher.get_price(additions, self._end_date, "1d", self._fq, self._count)
            FactorGen._open = pd.concat([FactorGen._open, temp["open"]], axis=1)
            FactorGen._close = pd.concat([FactorGen._close, temp["close"]], axis=1)
            FactorGen._high = pd.concat([FactorGen._high, temp["high"]], axis=1)
            FactorGen._low = pd.concat([FactorGen._low, temp["low"]], axis=1)
            FactorGen._volume = pd.concat([FactorGen._volume, temp["volume"]], axis=1)
            FactorGen._turnover = pd.concat([FactorGen._turnover, temp["turnover"]], axis=1)
            FactorGen._is_paused = pd.concat([FactorGen._is_paused, temp["is_paused"]], axis=1)

        return (
            self._open[code_].iloc[-count:],
            self._close[code_].iloc[-count:],
            self._high[code_].iloc[-count:],
            self._low[code_].iloc[-count:],
            self._volume[code_].iloc[-count:],
            self._turnover[code_].iloc[-count:],
            self._is_paused[code_].iloc[-count:],
        )


class Model(ABC):
    @abstractmethod
    def fit(self, *args, **kwargs):
        pass

    def dump(self, filename):
        with open(filename, "wb") as f:
            pickle.dump(self, f)

    @classmethod
    def load(cls, filename):
        with open(filename, "rb") as f:
            return pickle.load(f)
