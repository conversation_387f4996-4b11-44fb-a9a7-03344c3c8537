import inspect
import pathlib
from typing import Callable, Mapping, Optional, Sequence, Tuple, Union

import datetype
import pandas as pd
from loguru import logger
from shared_utils.kits import RunEnv, run_env

from aichemy.data_ops.pipeline import Apply<PERSON><PERSON>eline, DataLocation, ResearchPipeline
from aichemy.utils import slice_dataset

from .utils import generate_random_code


def dump(version, g, explicit_dir=None, **kwargs):
    """保存项目

    Args:
        version (str): 版本号
        g (dict): 全局变量
        explicit_dir (str, optional): 指定保存路径. Defaults to None.
    """
    project_id = generate_random_code()
    if explicit_dir is not None:
        path = f"{version}/{explicit_dir}"
    else:
        path = f"{version}/{project_id}"
    if pathlib.Path(path).exists():
        logger.error(f"项目路径：{path} 已经存在，请检查")
        return False
    else:
        logger.info(f"项目保存路径：{path}")
        pathlib.Path(path).mkdir(parents=True, exist_ok=True)
    kwargs.update({"project_id": project_id})
    with open(pathlib.Path(path, "src.txt"), "w") as f:
        for k, v in kwargs.items():
            if callable(v):
                f.write(inspect.getsource(v).replace(f"def {v.__name__}", f"def {k}") + "\n")
            else:
                f.write(f"{k} = {repr(v)}\n\n")
    load(path, g)
    return True


def load(prj_path, g):
    with open(pathlib.Path(prj_path, "src.txt"), "r") as f:
        exec(f.read(), g)
        exec(f"path = {repr(prj_path)}", g)


def get_start_datetime(trade_day: str, exchange: str) -> datetype.AwareDateTime:
    """获取交易所的开盘时间

    Args:
        trade_day (str): 交易日
        exchange (str): 交易所

    Raises:
        ValueError: 传入的日期不是交易日；传入的交易所不支持

    Returns:
        pd.Timestamp: 开盘时间
    """
    if run_env == RunEnv.LOCAL:
        if exchange not in ["SSE", "SZSE", "BSE", "CFFEX", "SHFE", "INE", "DCE", "CZCE", "GFEX"]:
            raise ValueError(f"交易所 {exchange} 不支持")
        try:
            import qnt_research.api as qnt_api

            tmp1 = qnt_api.calc_trade_day(exchange, trade_day, 0).strftime("%Y-%m-%d")
            tmp2 = pd.Timestamp(trade_day).strftime("%Y-%m-%d")
            if tmp1 == tmp2:
                return qnt_api.get_trade_day_open_time(exchange, trade_day)
            else:
                return qnt_api.get_trade_day_open_time(
                    exchange=exchange,
                    trade_day=qnt_api.calc_trade_day(exchange, tmp1, 1).strftime("%Y-%m-%d"),
                )
        except ImportError:
            logger.warning("当前交易时间不是从数据库获取")
            return pd.Timestamp(trade_day, tz="Asia/Shanghai").replace(hour=9, minute=15)  # type: ignore
    else:
        return pd.Timestamp(trade_day, tz="Asia/Shanghai").replace(hour=9, minute=15)  # type: ignore


def get_end_datetime(trade_day: str, exchange: str) -> datetype.AwareDateTime:
    """获取交易所的收盘时间

    Args:
        trade_day (str): 交易日
        exchange (str): 交易所

    Raises:
        ValueError: 传入的日期不是交易日；传入的交易所不支持

    Returns:
        pd.Timestamp: 收盘时间
    """
    if run_env == RunEnv.LOCAL:
        if exchange not in ["SSE", "SZSE", "BSE", "CFFEX", "SHFE", "INE", "DCE", "CZCE", "GFEX"]:
            raise ValueError(f"交易所 {exchange} 不支持")
        try:
            import qnt_research.api as qnt_api

            return qnt_api.calc_trade_day(exchange, trade_day, 0).replace(hour=15, minute=0)
        except ImportError:
            logger.warning("当前交易时间不是从数据库获取")
            return pd.Timestamp(trade_day, tz="Asia/Shanghai").replace(hour=15, minute=0)  # type: ignore
    else:
        return pd.Timestamp(trade_day, tz="Asia/Shanghai").replace(hour=15, minute=0)  # type: ignore


def process_train_data(
    data: Mapping[str, pd.DataFrame], start_dt: str, end_dt: str, path: Union[str, pathlib.Path] = "./test", **kwargs
):
    start_dt = (pd.Timestamp(start_dt) - pd.Timedelta(f"{kwargs['leading_days']}D")).strftime("%Y-%m-%d")
    train_data = {
        k: slice_dataset(
            v,
            get_start_datetime(start_dt, kwargs.get("exchange", "SSE")).strftime("%Y-%m-%d %H:%M:%S"),
            get_end_datetime(end_dt, kwargs.get("exchange", "SSE")).strftime("%Y-%m-%d %H:%M:%S"),
            "both",
        ).copy(deep=True)
        for k, v in data.items()
    }

    research_pipeline = ResearchPipeline(
        path,
        cs_x_scaling_method=kwargs.get("cs_x_scaling_method", "none"),
        cs_y_scaling_method=kwargs.get("cs_y_scaling_method", "cs_zscore"),
        cs_scaling_fillna=kwargs.get("cs_scaling_fillna", False),
        x_scaling_method=kwargs.get("x_scaling_method", "zscore"),
        y_scaling_method=kwargs.get("y_scaling_method", "zscore"),
        scaling_fillna=kwargs.get("scaling_fillna", False),
        num_steps=kwargs.get("num_steps", None),
        non_training_size=kwargs.get("non_training_size", 0.25),
        dump=True,
        drop_threshold_nan_ratio=kwargs.get("drop_threshold_nan_ratio", 1.0),
    )
    if not kwargs.get("idx", False):
        r_data = research_pipeline.process_data(
            train_data,
            shuffle=kwargs.get("shuffle", True),
            enable_log=kwargs.get("enable_log", True),
            chunk_num=kwargs.get("chunk_num", 1),
            func_train=kwargs.get("func_train", None),
            func_non_train=kwargs.get("func_non_train", None),
            need_reduce=True,
            inplace=True,
            release_memory=True,
            max_assemblers=kwargs.get("max_assemblers", 5),
        )
    else:
        r_data = research_pipeline.process_idx_data(
            train_data,
            shuffle=kwargs.get("shuffle", True),
            enable_log=kwargs.get("enable_log", True),
            func_train=kwargs.get("func_train", None),
            func_non_train=kwargs.get("func_non_train", None),
            need_reduce=True,
            inplace=True,
            release_memory=True,
            max_assemblers=kwargs.get("max_assemblers", 5),
        )
    if len(r_data[0]) == 0:
        return None

    if "ref" in kwargs:
        kwargs["ref"]["research_pipeline"] = research_pipeline

    return r_data


def predict_apply_data(model, data, start_dt, end_dt, path="./test", **kwargs) -> Tuple[pd.DataFrame, pd.DataFrame]:
    """返回的DataFrame, index为nstimestamp, 不是pd.Timestamp"""
    start_dt0 = (pd.Timestamp(start_dt) - pd.Timedelta(f"{kwargs['leading_days']}D")).strftime("%Y-%m-%d")
    start_dt0 = get_start_datetime(start_dt0, kwargs.get("exchange", "SSE")).strftime("%Y-%m-%d %H:%M:%S")
    start_dt = get_start_datetime(start_dt, kwargs.get("exchange", "SSE")).strftime("%Y-%m-%d %H:%M:%S")
    end_dt = get_end_datetime(end_dt, kwargs.get("exchange", "SSE")).strftime("%Y-%m-%d %H:%M:%S")

    apply_data = {k: slice_dataset(v, start_dt0, end_dt, "both").copy(deep=True) for k, v in data.items()}
    apply_pipeline = ApplyPipeline(path)
    if not kwargs.get("idx", False):
        apply_x, true_y = apply_pipeline.process_data(
            apply_data,
            enable_log=kwargs.get("enable_log", True),
            func_pred=kwargs.get("func_pred", None),
            inplace=True,
            release_memory=True,
            max_assemblers=kwargs.get("max_assemblers", 5),
        )
        apply_y_hat = model.predict(apply_x)
    else:
        apply_idx, data_x, data_y = apply_pipeline.process_idx_data(
            apply_data,
            enable_log=kwargs.get("enable_log", True),
            func_pred=kwargs.get("func_pred", None),
            inplace=True,
            release_memory=True,
            max_assemblers=kwargs.get("max_assemblers", 5),
        )
        apply_y_hat = model.predict(data_x, index=apply_idx)
        true_y = DataLocation(data_y).locate(apply_idx)[:, -1, :]

    return slice_dataset(apply_pipeline.reverse_engineering(apply_y_hat), start_dt, end_dt, "both"), slice_dataset(
        apply_pipeline.reverse_engineering(true_y), start_dt, end_dt, "both"
    )


def rolling(
    end_date: str,
    num: int,
    train_month: int,
    test_month: int,
    n_cores: int = 1,
    summary: Optional[Callable[[Sequence[Tuple[pd.DataFrame, Mapping]]], Tuple[pd.DataFrame, Mapping]]] = None,
    func: Optional[Callable[..., Tuple[pd.DataFrame, Mapping]]] = None,
    *args,
    **kwargs,
) -> Tuple[pd.DataFrame, Mapping]:
    """滚动训练模型

    Args:
        end_date (str): 截止时间
        num (int): 执行次数
        train_month (int): 训练月数
        test_month (int): 测试月数
        n_cores (int, optional): 并行进程数. Defaults to 1.
        summary (Optional[Callable[[Sequence[Tuple[pd.DataFrame, Mapping]]], Tuple[pd.DataFrame, Mapping]]], optional): 结果分析回调函数. Defaults to None.
        func (Optional[Callable[..., Tuple[pd.DataFrame, Mapping]]], optional): 一轮训练+测试回调函数. Defaults to None.

    Returns:
        Tuple[pd.DataFrame, Mapping]: 预测结果汇总和分析结果
    """
    ret = []
    try:
        dates = []
        ed = end_date
        for _ in range(num):
            md = (pd.Timestamp(ed) - pd.offsets.MonthEnd(test_month)).strftime("%Y-%m-%d")
            sd = (pd.Timestamp(md) - pd.offsets.MonthBegin(train_month)).strftime("%Y-%m-%d")
            dates.append((sd, md, ed))
            ed = (pd.Timestamp(ed) - pd.offsets.MonthEnd(test_month)).strftime("%Y-%m-%d")

        if func:
            n_cores = min(n_cores, len(dates))
            if n_cores == 1:
                for sd, md, ed in dates:
                    ret.append(func(sd, md, ed, *args, **kwargs))
            else:
                # from multiprocessing import Pool
                # pool = Pool(processes=n_cores)
                # for sd, md, ed in dates:
                #     ret.append(pool.apply_async(func, (sd, md, ed, *args), kwds=kwargs))
                # pool.close()
                # pool.join()
                # ret = [r.get() for r in ret]

                from concurrent.futures import ProcessPoolExecutor

                with ProcessPoolExecutor(max_workers=n_cores) as executor:
                    for sd, md, ed in dates:
                        ret.append(executor.submit(func, sd, md, ed, *args, **kwargs))
                ret = [r.result() for r in ret]

    except Exception as e:
        logger.exception(e)
    if ret:
        assert summary
        return summary(ret)
    else:
        return pd.DataFrame(), {}


def train_latest_model(train_dt, train_month, test_month, func, *args, **kwargs):
    sd = (pd.Timestamp(train_dt) - pd.offsets.MonthBegin(train_month)).strftime("%Y-%m-%d")
    ed = (pd.Timestamp(train_dt) + pd.offsets.MonthEnd(test_month)).strftime("expire[%Y-%m-%d]")
    func(sd, train_dt, ed, *args, **kwargs)


def save_predict_result(result: Sequence[pd.DataFrame], dump=True, file="./predict.csv") -> pd.DataFrame:
    """把rolling预测的结果保存到csv

    Args:
        result (Sequence[pd.DataFrame]): 列表中每个元素为每一次rolling过程的预测结果
        file (str, optional): 保存文件. Defaults to "./predict.csv".

    Returns:
        pd.DataFrame: 全部的预测结果, index为datetime，columns为股票代码
    """
    result_ = pd.concat(result, axis=0).sort_index()
    result_.index = result_.index.map(lambda x: pd.to_datetime(x, utc=True).tz_convert("Asia/Shanghai"))
    if dump:
        result_.to_csv(file)
    return result_
