import copy
import pathlib
import random
import re
from functools import lru_cache, reduce
from typing import Iterable, Literal, Mapping, Optional, Sequence, Tuple, Union

import numpy as np
import pandas as pd
from loguru import logger
from shared_utils.enums import Exchange
from shared_utils.label import QSymbolParser
from shared_utils.typedef import QSymbol

from .factor_analyse.analyse import orthogonalize_factor
from .utils import acquire_flock, slice_dataset


def _get_ftype(factor_cache_file: str) -> str:
    if (tmp := re.search(r"^[^\.]+\.([^\.]+)\.h5", factor_cache_file)) is None:
        raise ValueError(f"Invalid factor cache file: {factor_cache_file}")
    return tmp.group(1)


def to_key(database: str, name1: str, name2: str, shift: Optional[int] = None) -> str:
    if shift is not None:
        return f"/{database}/{name1}/{name2}/{shift}"
    else:
        return f"/{database}/{name1}/{name2}"


def from_key(key: str) -> Tuple[str, str, str, int]:
    """返回值分别为database, name1, name2, shift"""
    if (tmp := re.search(r"^/([^/]+)/([^/]+)/([^/]+)(?:/(\d)+)?$", key)) is not None:
        db, n1, n2, shift = tmp.groups()
        return db, n1, n2, int(shift or "0")
    raise ValueError(f"Invalid key: {key}")


@lru_cache
def get_factor_dict(factor_cache_file: str, database: str) -> Mapping[str, Sequence[str]]:
    """获取目前已经保存到本地的因子字典

    Args:
        factor_cache_file (str): 因子缓存文件.
        database (str): 数据库名.

    Returns:
        Dict[str, List[str]]: {'量价强化学习类': ['RL_DAY_MF_ALPHA6', 'RL_DAY_MF_ALPHA11', 'RL_DAY_MF_ALPHA7']}
    """
    _get_ftype(factor_cache_file)
    factor_cache_path = pathlib.Path(factor_cache_file)
    factor_cache_path.parent.mkdir(parents=True, exist_ok=True)
    ret = {}
    with acquire_flock(factor_cache_path.with_suffix(".lock"), "SH"):
        if factor_cache_path.exists():
            with pd.HDFStore(factor_cache_file, mode="r") as store:
                for key in store.keys():
                    try:
                        db, n1, n2, _ = from_key(key)
                        if db == database:
                            ret.setdefault(n1, set()).add(n2)
                    except ValueError:
                        continue
    return {k: list(v) for k, v in ret.items()}


def del_key(key: str, factor_cache_file: str):
    _get_ftype(factor_cache_file)
    with acquire_flock(pathlib.Path(factor_cache_file).with_suffix(".lock"), "EX"):
        with pd.HDFStore(factor_cache_file, mode="a") as store:
            store.remove(key)  # type: ignore
    get_factor_dict.cache_clear()


def del_factor(name1: str, name2: str, factor_cache_file: str, database: str) -> None:
    """删除因子

    Args:
        name1 (str): 因子大类
        name2 (str): 因子名称
        factor_cache_file (str): 因子缓存文件.
        database (str): 数据库名.
    """
    key = to_key(database, name1, name2)
    del_key(key, factor_cache_file)


def encode_stock(qsymbol: QSymbol) -> int:
    pure_code, exchange = qsymbol.split(".")
    return {"SSE": 1, "SZSE": 2, "BSE": 3}[exchange] * 10 ** (len(pure_code)) + int(pure_code)


def decode_stock(code: int) -> QSymbol:
    n = len(str(code)) - 1
    return "{x1:0>{x2}}.{x3}".format(x1=code % 10**n, x2=n, x3={1: "SSE", 2: "SZSE", 3: "BSE"}[code // 10**n])


def save_data_to_store(key: str, data: pd.DataFrame, store_file: str, mode: str = "a"):
    ftype = _get_ftype(store_file)

    data = data.copy().sort_index()
    # 数据中可能存在缺失值，处理方式是丢弃最后几行全是缺失值的数据，中间的保留，因为有些因子在9:10分更新，但是在这之后数据库已经有记录了
    finite_rows = np.where(data.notna().any(axis=1))[0]
    if finite_rows.size == 0:
        logger.info(f"{key} is empty")
        return
    else:
        data = data.iloc[: finite_rows.max() + 1]

    exchanges = data.columns.map(QSymbolParser.get_exchange).unique()
    if exchanges.isin([Exchange.SSE, Exchange.SZSE, Exchange.BSE]).all() and ftype == "stock":
        data.columns = data.columns.map(encode_stock)
    else:
        raise

    with acquire_flock(pathlib.Path(store_file).with_suffix(".lock"), "EX"):
        with pd.HDFStore(store_file, mode="a") as store:
            if key in store:
                if mode == "a":
                    tmp: pd.DataFrame = store.get(key)  # type: ignore
                    nan_rows = tmp.index[tmp.isna().all(axis=1)]
                    new_symbols = data.columns.difference(tmp.columns)
                    rewrite_rows = data.index.intersection(nan_rows)
                    if new_symbols.size > 0 or rewrite_rows.size > 0:
                        if new_symbols.size > 0:
                            logger.info(f"{key} 新增 symbol: {','.join(str(symbol) for symbol in new_symbols)}")
                        if rewrite_rows.size > 0:
                            logger.info(f"{key} 重写 {','.join(str(row) for row in rewrite_rows)} 行")
                        store.remove(key)  # type: ignore
                        data = pd.concat([tmp.loc[tmp.index.difference(data.index)], data], axis=0).sort_index()
                        store.put(key=key, value=data, format="table")
                    else:
                        data = data.loc[~data.index.isin(tmp.index)].reindex(columns=tmp.columns)
                        store.append(key, data)
                else:
                    store.remove(key)  # type: ignore
                    store.put(key=key, value=data, format="table")
            else:
                store.put(key=key, value=data, format="table")
        if not data.empty:
            text = "{}保存完成, mode：{}, 时间：{} - {}".format(
                key,
                mode,
                *map(
                    lambda x: pd.to_datetime(x, utc=True).tz_convert("Asia/Shanghai").strftime("%Y-%m-%d %H:%M:%S"),
                    [data.index.min(), data.index.max()],
                ),
            )
            logger.info(text)
        else:
            logger.info(f"{key}本地数据已存在，跳过！")
    get_factor_dict.cache_clear()


class FactorOrthogonalizer:
    def __init__(self, x_name: Sequence[Tuple[str, str, str]], factor_cache_file: str) -> None:
        self.x_name = x_name
        self.factor_cache_file = factor_cache_file

        # 检查是否已存在相同的x_name配置
        existing_config = self._find_existing_config()
        if existing_config:
            self.orth_name = existing_config
        else:
            # 如果没有找到匹配的配置或加载失败，创建新配置
            self.orth_name = self._generate_new_orth_name()
            self._save_to_hdf()

        # 初始化并保存
        self.x_list = []
        for db, n1, n2 in self.x_name:
            self.x_list.append(get_factor(n1, n2, database=db, factor_cache_file=factor_cache_file))
        self.idx = reduce(lambda x, y: x.intersection(y), [i.index for i in self.x_list]).sort_values(ascending=True)

    @classmethod
    def _get_config_df(
        cls, factor_cache_file: str, mode: Literal["a", "w", "r", "r+"] = "r"
    ) -> Tuple[Optional[pd.DataFrame], Optional[pd.HDFStore]]:
        """获取配置表DataFrame和HDFStore对象

        Args:
            factor_cache_file (str): 因子缓存文件路径
            mode (Literal['a', 'w', 'r', 'r+']): 打开模式，默认为"r"只读模式

        Returns:
            Tuple[Optional[pd.DataFrame], Optional[pd.HDFStore]]: 配置表DataFrame和HDFStore对象
        """
        try:
            store = pd.HDFStore(factor_cache_file, mode=mode)
            config_key = "/orth/config"

            if config_key not in store:
                return None, store

            config_df = store.get(config_key)
            return config_df, store  # type: ignore
        except Exception as e:
            logger.error(f"获取配置表失败: {e}")
            return None, None

    @classmethod
    def get_orths(cls, factor_cache_file: str) -> Optional[Mapping[str, Sequence[Tuple[str, str, str]]]]:
        """获取所有正交化配置

        Args:
            factor_cache_file (str): 因子缓存文件路径

        Returns:
            Optional[Mapping[str, Sequence[Tuple[str, str, str]]]]: 所有正交化配置
        """
        with acquire_flock(pathlib.Path(factor_cache_file).with_suffix(".lock"), "SH"):
            config_df, store = cls._get_config_df(factor_cache_file)
            if store is not None:
                store.close()
            if config_df is None or config_df.empty:
                return {}
            return {
                orth_name: eval(x_name_str)
                for orth_name, x_name_str in zip(config_df["orth_name"], config_df["x_name_str"])
            }

    def _find_existing_config(self) -> Optional[str]:
        """查找是否已存在相同的x_name配置

        Returns:
            Optional[str]: 如果找到匹配的配置，返回对应的orth_name；否则返回None
        """
        try:
            with acquire_flock(pathlib.Path(self.factor_cache_file).with_suffix(".lock"), "SH"):
                config_df, store = self.__class__._get_config_df(self.factor_cache_file)
                if store is not None:
                    try:
                        if config_df is None or config_df.empty:
                            return None

                        # 使用向量化操作检查是否有匹配的配置
                        x_name_str = str(sorted(self.x_name))
                        matches = config_df[config_df["x_name_str"] == x_name_str]

                        if not matches.empty:
                            return matches.iloc[0]["orth_name"]
                    finally:
                        store.close()
            return None
        except Exception as e:
            logger.error(f"查找正交化配置失败: {e}")
            return None

    def _generate_new_orth_name(self) -> str:
        """生成新的orth_name

        Returns:
            str: 新生成的orth_name
        """
        try:
            with acquire_flock(pathlib.Path(self.factor_cache_file).with_suffix(".lock"), "SH"):
                config_df, store = self.__class__._get_config_df(self.factor_cache_file)
                if store is not None:
                    try:
                        if config_df is not None and not config_df.empty:
                            # 使用正则表达式和向量化操作提取数字部分
                            orth_names = config_df["orth_name"].tolist()
                            max_num = 0

                            # 使用列表推导式和正则表达式提取数字
                            nums = [
                                int(tmp.group())
                                for name in orth_names
                                if name.startswith("orth") and (tmp := re.search(r"\d+", name)) is not None
                            ]

                            if nums:
                                max_num = max(nums)

                            return f"orth{max_num + 1}"
                    finally:
                        store.close()
            return "orth0"  # 如果没有找到配置或出错，返回默认值
        except Exception as e:
            logger.error(f"生成新的orth_name失败: {e}")
            return "orth0"

    def _save_to_hdf(self) -> None:
        """将x_name和orth_name信息保存到HDF文件中，只有在新增orth_name时才执行保存"""
        with acquire_flock(pathlib.Path(self.factor_cache_file).with_suffix(".lock"), "EX"):
            config_df, store = self.__class__._get_config_df(self.factor_cache_file, mode="a")
            if store is not None:
                try:
                    config_key = "/orth/config"
                    x_name_str = str(sorted(self.x_name))

                    if config_df is not None and not config_df.empty:
                        # 使用向量化操作检查是否已存在相同的orth_name
                        if self.orth_name in config_df["orth_name"].values:
                            logger.info(f"orth_name {self.orth_name} 已存在，不进行修改")
                            return

                        # 使用向量化操作检查是否已存在相同的x_name配置
                        if (config_df["x_name_str"] == x_name_str).any():
                            matching_row = config_df[config_df["x_name_str"] == x_name_str].iloc[0]
                            logger.info(f"相同的x_name配置已存在于orth_name {matching_row['orth_name']}，不进行保存")
                            return

                        # 新增配置
                        new_config = pd.DataFrame({"orth_name": [self.orth_name], "x_name_str": [x_name_str]})
                        config_df = pd.concat([config_df, new_config], ignore_index=True)
                    else:
                        # 创建新的配置表
                        config_df = pd.DataFrame({"orth_name": [self.orth_name], "x_name_str": [x_name_str]})

                    # 保存配置
                    store.put(key=config_key, value=config_df, format="table")
                    logger.info(f"新的正交化配置 {self.orth_name} 已保存到: {config_key}")
                finally:
                    store.close()

    def _load_from_hdf(self) -> bool:
        """从HDF文件中加载x_name信息

        Returns:
            bool: 是否成功加载
        """
        try:
            with acquire_flock(pathlib.Path(self.factor_cache_file).with_suffix(".lock"), "SH"):
                config_df, store = self.__class__._get_config_df(self.factor_cache_file)
                if store is not None:
                    store.close()
                    if config_df is not None:
                        # 使用向量化操作查找匹配的orth_name
                        match = config_df[config_df["orth_name"] == self.orth_name]
                        if not match.empty:
                            # 加载x_name列表
                            self.x_name = eval(match["x_name_str"].iloc[0])

                            # 加载因子数据
                            self._load_factor_data()
                            return True
            return False
        except Exception as e:
            logger.error(f"从HDF加载正交化配置失败: {e}")
            return False

    def _load_factor_data(self) -> None:
        """加载因子数据"""
        self.x_list = []
        for db, n1, n2 in self.x_name:
            try:
                self.x_list.append(get_factor(n1, n2, database=db, factor_cache_file=self.factor_cache_file))
            except Exception as e:
                logger.error(f"加载因子数据失败 ({db}, {n1}, {n2}): {e}")
                raise

        if self.x_list:
            self.idx = reduce(lambda x, y: x.intersection(y), [i.index for i in self.x_list]).sort_values(
                ascending=True
            )
        else:
            logger.warning("没有成功加载任何因子数据")
            self.idx = pd.Index([])

    @classmethod
    def from_orth_name(cls, orth_name: str, factor_cache_file: str) -> Optional["FactorOrthogonalizer"]:
        """通过orth_name从HDF文件中还原实例

        Args:
            orth_name (str): 正交化配置名称
            factor_cache_file (str): 因子缓存文件路径

        Returns:
            FactorOrthogonalizer: 正交化器实例
        """
        # 创建一个空实例
        instance = cls.__new__(cls)
        instance.x_list = []
        instance.factor_cache_file = factor_cache_file
        instance.orth_name = orth_name

        # 加载配置
        if instance._load_from_hdf():
            logger.info(f"从HDF文件中加载了正交化配置: {instance.orth_name}")
            return instance
        else:
            logger.error(f"无法从HDF文件中加载正交化配置: {orth_name}")
            return None

    def orthogonalize_factor(
        self,
        factor_df: Union[str, pd.DataFrame],
        start_dt: Optional[str] = None,
        end_dt: Optional[str] = None,
        dump: bool = False,
    ) -> pd.DataFrame:
        """对因子df进行市值和行业因子正交化

        Args:
            factor_df (Union[str, pd.DataFrame]): 待正交化的因子数据，或者因子数据的HDF键名
            start_dt (Optional[str], optional): 开始日期，格式为'YYYY-MM-DD'。默认为None，表示使用全部数据
            end_dt (Optional[str], optional): 结束日期，格式为'YYYY-MM-DD'。默认为None，表示使用全部数据
            dump (bool, optional): 是否将正交化后的因子数据保存到HDF文件中。默认为False

        Returns:
            pd.DataFrame: 正交化后的因子数据
        """
        if dump:
            if not isinstance(factor_df, str):
                raise ValueError("dump=True时，factor_df必须为HDF键名")
        if isinstance(factor_df, str):
            db, l1n, l2n, shift = from_key(factor_df)
            factor_df = get_factor(l1n, l2n, database=db, factor_cache_file=self.factor_cache_file, shift=shift)
        # 根据日期范围筛选数据
        factor_df = slice_dataset(factor_df, start_dt and f"{start_dt} 000000", end_dt and f"{end_dt} 235959")
        # 获取交集索引
        idx = self.idx.intersection(factor_df.index)

        # 对因子进行正交化
        res = orthogonalize_factor(factor_df.loc[idx], [i.loc[idx] for i in self.x_list], 1, 0, 1, False)
        if dump:
            key = to_key(self.orth_name, l1n, l2n)
            save_data_to_store(key, res, self.factor_cache_file)
        return res


def get_factor(
    name1: str,
    name2: str,
    database: str,
    factor_cache_file: str,
    start_dt: Optional[str] = None,
    end_dt: Optional[str] = None,
    shift: int = 0,
) -> pd.DataFrame:
    """取因子数据

    Args:
        name1 (str): 因子大类名称
        name2 (str): 因子名称
        database (str): 数据库名
        factor_cache_file (str, optional): 因子缓存文件. Defaults to DEFAULT_FACTOR_CACHE_FILE.
        start_dt (Optional[str], optional): 开始日期，None则取到最早，为None时如果因子数据不存在则报错. Defaults to None.
        end_dt (Optional[str], optional): 结束日期，None则取到最后，为None时如果因子数据不存在则报错. Defaults to None.
        shift (int, optional): 偏移量. Defaults to 0.

    Returns:
        pd.DataFrame: 因子数据
    """
    if not pathlib.Path(factor_cache_file).exists():
        raise FileNotFoundError(f"Factor cache file not found: {factor_cache_file}")

    factor_dict = get_factor_dict(factor_cache_file, database)
    if name1 not in factor_dict or name2 not in factor_dict[name1]:
        raise KeyError(f"Factor not found: {database} -> {name1} -> {name2}")

    df = _get_factor(factor_cache_file, database, name1, name2).shift(shift)
    df = slice_dataset(df, start_dt and f"{start_dt} 000000", end_dt and f"{end_dt} 235959", "both")
    return df


def _get_factor(factor_cache_file: str, database: str, name1: str, name2: str) -> pd.DataFrame:
    ftype = _get_ftype(factor_cache_file)
    key = to_key(database, name1, name2)
    with acquire_flock(pathlib.Path(factor_cache_file).with_suffix(".lock"), "SH"):
        with pd.HDFStore(factor_cache_file, mode="r") as store:
            df = store[key].sort_index().copy()
            if ftype == "stock":
                df.columns = df.columns.map(decode_stock)
            else:
                raise
    return df  # type: ignore


def check_db(database: str, factor_cache_file: str, full_index: pd.Index) -> Mapping[str, Sequence[str]]:
    factor_dict = get_factor_dict(factor_cache_file, database)
    result = {}
    for n1 in factor_dict:
        for n2 in factor_dict[n1]:
            data = get_factor(n1, n2, database=database, factor_cache_file=factor_cache_file)
            if data.empty:
                result[n1] = []
                logger.warning(f"{n1} {n2} 为空")
            else:
                idx1 = pd.to_datetime(data.dropna(how="all", axis=0).index)
                idx2 = pd.to_datetime(data.index)
                tmp = full_index[(full_index >= idx2.min()) & (full_index <= idx2.max())]
                tmp = tmp.difference(idx1)
                if not tmp.empty:
                    result.setdefault(n1, []).extend(tmp)
                    logger.warning(f"{n1} {n2} 有缺失数据：{tmp}")
    return result


def factor_df_gnt(
    store_file: str,
    date_interval: Iterable[Tuple[str, str]],
    factor_list: Iterable[Tuple[str, str, str, str]],
    masks: Optional[Iterable] = None,
):
    """hxf因子生成器，遍历所有hxf因子，除了集合竞价量价类和Transformer量价预测类"""
    for db, level1_names, level2_names_, shifts_ in factor_list:
        factor_dict = get_factor_dict(store_file, database=db)
        level1_names = level1_names.split(",")
        for l1n in level1_names:
            if level2_names_ == "*":
                level2_names = list(copy.deepcopy(factor_dict.get(l1n, [])))
            else:
                level2_names = level2_names_.split(",")
            shifts = list(map(int, shifts_.split(",")))

            if l1n in ["集合竞价量价类", "Transformer量价预测类"]:
                continue
            random.shuffle(level2_names)
            for factor in level2_names:
                factor_df = get_factor(name1=l1n, name2=factor, database=db, factor_cache_file=store_file)
                for mask_call in masks or [lambda x: (x, {})]:
                    factor_df, info = mask_call(factor_df)
                    for s in shifts:
                        factor_df_shift = factor_df.shift(s)
                        key = to_key(db, l1n, factor, s)
                        for start_dt, end_dt in date_interval:
                            yield (
                                (start_dt, end_dt),
                                key,
                                slice_dataset(factor_df_shift, f"{start_dt} 090000", f"{end_dt} 150000", "both"),
                                info,
                            )
