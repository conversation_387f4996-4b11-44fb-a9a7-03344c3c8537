import functools
import json
from typing import Dict, List, Literal, Optional

import numpy as np
import pandas as pd
from shared_utils.enums import FinancialAsset
from shared_utils.kits import RunEnv, run_env
from shared_utils.label import QSymbolParser, SymbolConverter
from shared_utils.typedef import QSymbol

from .database_manager import get_factor
from .utils import to_nstimestamp

FIELDS_OF_FUTURES = ["open", "close", "high", "low", "volume", "turnover", "is_paused", "open_interest"]
FIELDS_OF_STOCK = ["open", "close", "high", "low", "volume", "turnover", "is_paused"]


class Adapter:
    pass


class SupermindAdapter(Adapter):
    @staticmethod
    def get_contract_multiplier(code: QSymbol) -> float:
        import mindgo_api as mg

        if QSymbolParser.get_financial_asset(code) == FinancialAsset.FUTURES:
            return mg.get_security_info(SymbolConverter.qs_to_mindgo(code)).contract_multiplier
        else:
            return 1.0

    @staticmethod
    def get_idx(start_dt: Optional[str], end_date: str, count: int = 0) -> pd.Index:
        from mindgo_api import get_trade_days

        return (
            get_trade_days(start_dt, end_date, count).tz_localize(tz="Asia/Shanghai") + pd.Timedelta(hours=15)
        ).astype(int)

    @staticmethod
    def get_all_symbols(dt: str) -> List[str]:
        from mindgo_api import get_all_securities

        return get_all_securities("stock", date=dt).index.map(SymbolConverter.qs_from_mindgo).tolist()


class LocalDBAdapter(Adapter):
    @staticmethod
    def get_contract_multiplier(code: QSymbol) -> float:
        import qnt_research.api as qnt

        if QSymbolParser.get_financial_asset(code) == FinancialAsset.FUTURES:
            if (ret := qnt.get_contract_info(code)) is None:
                raise RuntimeError(f"get_contract_multiplier: 无法获取合约信息 {code}")
            return ret["contract_multiplier"]
        else:
            return 1.0

    @staticmethod
    def get_idx(start_dt: Optional[str], end_date: str, count: int = 0) -> pd.Index:
        import qnt_research.api as qnt

        return pd.Index(
            map(lambda x: x.replace(hour=15), qnt.get_trade_days("SHFE", start_dt, end_date, count))
        ).astype(int)

    @staticmethod
    def get_all_symbols(dt: str) -> List[str]:
        import qnt_research.api as qnt

        return qnt.get_all_ifut(dt=dt)


if run_env == RunEnv.SUPERMIND:
    _adapter = SupermindAdapter
else:
    _adapter = LocalDBAdapter


@functools.lru_cache(maxsize=128)
def get_contract_multiplier(code) -> float:
    """获取合约乘数"""
    return _adapter.get_contract_multiplier(code)


@functools.lru_cache(maxsize=128)
def get_idx(start_dt: Optional[str], end_date: str, count: int = 0) -> pd.Index:
    """获取指定时间段内的因子df的index，格式为nstimestamp

    Args:
        start_dt (str | None): 开始时间, 与count同时存在时，忽略count
        end_date (str): 结束时间
        count (int, optional): 自end_date往前多少个交易日. Defaults to 0.

    Returns:
        pd.Index: 指定时间段内的因子df的index，格式为nstimestamp
    """
    return _adapter.get_idx(start_dt, end_date, count)


def dump_nhci():
    """获取南华商品指数"""
    import json

    import requests

    ret = requests.get("http://nanhua.net/ianalysis/varietyindex/index/NHCI.json?t=1718471626788").text
    with open("/workspace/南华商品指数.txt", "w") as f:
        json.dump(json.loads(ret), f)


@functools.lru_cache(maxsize=32)
def _get_bench(benchmark: str, end_date: str, frequency: str, count: int) -> pd.DataFrame:
    """以南华商品指数作为基准, 1d频率"""
    ret = pd.DataFrame(np.nan, index=get_idx(None, end_date, count), columns=["open", "close"])

    # 读取本地南华商品指数json文件, 转成DataFrame, index为ns时间戳
    with open("/workspace/南华商品指数.txt", "r") as f:
        nh = json.load(f)
    nh = pd.DataFrame(nh).drop_duplicates(0, keep="last")
    nh.set_index(((nh[0] + 15 * 60 * 60 * 1000) * 1e6).astype(int), inplace=True)

    # 由于南华商品指数可能会有数据缺失和重复, 因此获取起止范围内的完整交易日, 把数据补齐
    st = pd.Timestamp.fromtimestamp(nh[0].iloc[0] / 1000, tz="Asia/shanghai").strftime("%Y-%m-%d")
    ed = pd.Timestamp.fromtimestamp(nh[0].iloc[-1] / 1000, tz="Asia/shanghai").strftime("%Y-%m-%d")
    tmp = pd.DataFrame(index=get_idx(st, ed, 0))
    tmp[0] = nh.loc[:, 1]
    tmp[0] = tmp[0].ffill()  # 0是close
    tmp[1] = tmp[0].shift(1)  # 1是open

    # 把补齐的数据截断放入ret
    index = list(tmp.index.intersection(ret.index))
    ret.loc[index, "close"] = tmp.loc[index, 0]
    ret.loc[index, "open"] = tmp.loc[index, 1]
    return ret.sort_index()


def get_all_symbols(dt: str) -> List[str]:
    """获取指定日期的所有加权指数合约代码

    Args:
        dt (str): 日期

    Returns:
        List[str]: 指定日期的所有加权指数合约代码
    """
    return _adapter.get_all_symbols(dt)


class DataFetcher:
    type_: FinancialAsset
    fields: List[str]

    def __init__(self, type_: Literal["stock", "futures"]):
        self.type_ = FinancialAsset[type_]
        self.fields = {FinancialAsset.STOCK: FIELDS_OF_STOCK, FinancialAsset.FUTURES: FIELDS_OF_FUTURES}[self.type_]

    def get_bench(*args, **kwargs) -> Dict[str, pd.DataFrame]: ...

    def get_price(*args, **kwargs) -> Dict[str, pd.DataFrame]: ...


class DataFetcherOfQNT(DataFetcher):
    """因子计算所需的数据从Postgres数据库中获取

    Examples:
        >>> from aichemy.data_fetcher import DataFetcherOfQNT
        >>> from aichemy.factor_gen.alpha_191 import Alpha191
        >>> data_fetcher = DataFetcherOfQNT()
        >>> al191 = Alpha191(data_fetcher)
    """

    def __init__(self):
        super().__init__("futures")

    def get_bench(
        self,
        symbol_list: List[str],
        benchmark: str,
        end_date: str,
        frequency: str,
        count: int = 0,
    ) -> Dict[str, pd.DataFrame]:
        """获取基准数据

        Args:
            symbol_list (List[str]): 代码列表
            benchmark (str): 基准指数，这里基准指数无效，默认采用南华商品指数
            end_date (str): 时间
            frequency (str): 频率
            count (int, optional): 自end_date往前多少个交易日. Defaults to 0.

        Returns:
            Dict[str, pd.DataFrame]: 基准数据。key - benchmark_open和benchmark_close, value - DataFrame, index为ns时间戳, columns为symbol
        """
        temp = _get_bench(benchmark, end_date, frequency, count)
        bio = pd.DataFrame()
        bic = pd.DataFrame()
        for stock in symbol_list:
            bio[stock] = temp["open"]
            bic[stock] = temp["close"]
        idx = get_idx(None, end_date, count)
        return {
            "benchmark_open": bio.reindex(index=idx).sort_index(),
            "benchmark_close": bic.reindex(index=idx).sort_index(),
        }

    @functools.lru_cache(maxsize=128)
    def _get_price(
        self,
        symbol: str,
        end_date: str,
        frequency: str,
        fq: Optional[Literal["forward", "backward"]],
        count: int,
    ) -> pd.DataFrame:
        """获取数据

        Args:
            symbol (str): 代码
            end_date (str): 结束时间
            frequency (str): 数据频率
            fq (Optional[Literal[&quot;forward&quot;, &quot;backward&quot;]]): 复权
            count (int): 自end_date往前多少个交易日

        Returns:
            pd.DataFrame: index - ["symbol", "time"], time为ns时间戳, columns - ["open", "close", "high", "low", "volume", "turnover", "is_paused","open_interest"]
        """
        import qnt_research.api as qnt

        ret = qnt.get_price(symbol, None, end_date, frequency, self.fields, False, fq, count)
        ret["volume"] = ret["volume"].astype(float)
        ret["turnover"] = ret["volume"] * (ret["open"] / 2 + ret["close"] / 2) * get_contract_multiplier(symbol)
        ret["time"] = ret.index
        ret["symbol"] = symbol
        ret = ret.set_index(["time", "symbol"])
        return ret

    def get_price(
        self,
        symbol_list: List[str],
        end_date: str,
        frequency: str,
        fq: Optional[Literal["forward", "backward"]],
        count: int = 0,
    ) -> Dict[str, pd.DataFrame]:
        """获取数据

        Args:
            symbol_list (List[str]): 代码列表
            end_date (str): 结束时间
            frequency (str): 数据频率
            fq (Optional[Literal[&quot;forward&quot;, &quot;backward&quot;]]): 复权
            count (int, optional): 自end_date往前多少个交易日. Defaults to 0.

        Raises:
            RuntimeError: 起止时间段内有数据缺失

        Returns:
            Dict[str, pd.DataFrame]: key - _FIELDS_OF_GET_PRICE, value - pd.DataFrame, index为nstimestamp, columns为symbol
        """
        tmp = pd.concat(
            [
                tmp
                for i in symbol_list
                if not (tmp := DataFetcherOfQNT._get_price(i, end_date, frequency, fq, count)).empty
            ],
            axis=0,
        ).unstack("symbol")
        tmp.index.rename(None, inplace=True)
        idx = get_idx(None, end_date, count)
        for nstimestamp in idx:
            if nstimestamp not in tmp.index:
                raise RuntimeError(f"没有找到时间为 {nstimestamp} 的行情数据")
        tmp.columns.rename((None, None), inplace=True)

        # 填充symbol缺失数据
        for field in FIELDS_OF_FUTURES:
            columns = pd.MultiIndex.from_product([[field], list(set(symbol_list) - set(tmp[field].columns))])
            tmp = pd.concat([tmp, pd.DataFrame(np.nan, index=tmp.index, columns=columns)], axis=1)

        tmp = tmp.reindex(index=idx, fill_value=np.nan).sort_index()
        return {i: tmp[i] for i in tmp.columns.levels[0]}


class DataFetcherOfMemory(DataFetcher):
    def __init__(
        self,
        symbol_list: List[str],
        end_date: str,
        frequency: str,
        fq: Optional[Literal["forward", "backward"]],
        count: int = 0,
    ):
        self.symbol_list = symbol_list
        self.frequency = frequency
        self.fq = fq

        self._data = DataFetcherOfQNT.get_price(symbol_list, end_date, frequency, fq, count)
        self._data.update(DataFetcherOfQNT.get_bench(symbol_list, "", end_date, frequency, count))

    def get_price(
        self,
        symbol_list: List[str],
        end_date: str,
        frequency: str,
        fq: Optional[Literal["forward", "backward"]],
        count: int = 0,
    ) -> Dict[str, pd.DataFrame]:
        if len(tmp := set(symbol_list).difference(set(self.symbol_list))) > 0:
            raise RuntimeError(f"没有找到代码为 {','.join(tmp)} 的行情数据")

        if fq != self.fq:
            raise RuntimeError(f"复权方式不一致，{fq} != {self.fq}")

        if frequency != self.frequency:
            raise RuntimeError(f"数据频率不一致，{frequency} != {self.frequency}")

        idx = get_idx(None, end_date, count)

        if idx.difference(self._data["open"].index).size > 0:
            raise RuntimeError("时间段内有数据缺失")

        return {i: self._data[i].reindex(index=idx, columns=symbol_list, fill_value=np.nan) for i in FIELDS_OF_FUTURES}

    def get_bench(
        self,
        symbol_list: List[str],
        benchmark: str,
        end_date: str,
        frequency: str,
        count: int = 0,
    ) -> Dict[str, pd.DataFrame]:
        if len(tmp := set(symbol_list).difference(set(self.symbol_list))) > 0:
            raise RuntimeError(f"没有找到代码为 {','.join(tmp)} 的行情数据")

        if frequency != self.frequency:
            raise RuntimeError(f"数据频率不一致，{frequency} != {self.frequency}")

        idx = get_idx(None, end_date, count)

        if idx.difference(self._data["benchmark_open"].index).size > 0:
            raise RuntimeError("时间段内有数据缺失")

        return {
            "benchmark_open": self._data["benchmark_open"].reindex(index=idx, columns=symbol_list, fill_value=np.nan),
            "benchmark_close": self._data["benchmark_close"].reindex(index=idx, columns=symbol_list, fill_value=np.nan),
        }


class DataFetcherOfLocalDB(DataFetcher):
    def __init__(self, type_, factor_cache_file: str, database: str):
        super().__init__(type_)
        self._factor_cache_file = factor_cache_file
        self._database = database

    def get_price(
        self, symbol_list: List[str], end_date: str, frequency: str, fq: Optional[str], count: int = 0
    ) -> Dict[str, pd.DataFrame]:
        ed_timestamp = to_nstimestamp(f"{end_date} 15:00:00")
        ret = {}
        for field in self.fields:
            data = get_factor("行情", field, self._database, self._factor_cache_file)
            idx = data.index.get_indexer([ed_timestamp], method="ffill").item()
            data = data.iloc[idx + 1 - count : idx + 1]
            ret[field] = data.reindex(columns=symbol_list, fill_value=np.nan)
        return ret

    def get_bench(
        self, symbol_list: List[str], benchmark: str, end_date: str, frequency: str, count: int = 0
    ) -> Dict[str, pd.DataFrame]:
        ed_timestamp = to_nstimestamp(f"{end_date} 15:00:00")
        bio = get_factor("benchmark", f"{benchmark}_open", self._database, self._factor_cache_file)
        idx = bio.index.get_indexer([ed_timestamp], method="ffill").item()
        bio = bio.iloc[idx + 1 - count : idx + 1]
        bic = get_factor("benchmark", f"{benchmark}_close", self._database, self._factor_cache_file)
        idx = bic.index.get_indexer([ed_timestamp], method="ffill").item()
        bic = bic.iloc[idx + 1 - count : idx + 1]
        return {
            "benchmark_open": bio.reindex(columns=symbol_list, fill_value=np.nan),
            "benchmark_close": bic.reindex(columns=symbol_list, fill_value=np.nan),
        }
