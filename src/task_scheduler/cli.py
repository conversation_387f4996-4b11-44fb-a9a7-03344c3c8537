#!/usr/bin/env python3
"""
Task Scheduler CLI 工具
整合了所有命令行功能，包括任务监控、管理和删除功能
"""

import os
import sys
import json
import click
import time
from typing import List, Dict, Any, Optional
from datetime import datetime

# 添加父目录到sys.path，以便导入task_scheduler包
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), "../")))

from task_scheduler.models import Task, TaskStatus
from task_scheduler.storage import TaskStorage
from task_scheduler.utils import format_time_delta, build_dependency_graph, get_task_execution_order


@click.group()
def cli():
    """任务调度监控和管理工具"""
    pass


@cli.command()
@click.option("--file", "-f", required=True, help="任务状态文件路径")
def list(file):
    """列出所有任务"""
    storage = TaskStorage(file)
    tasks = storage.get_all_tasks()

    if not tasks:
        click.echo("没有找到任务")
        return

    # 按状态分组
    waiting_tasks = [task for task in tasks if task.status == TaskStatus.WAITING]
    ready_tasks = [task for task in tasks if task.status == TaskStatus.READY]
    running_tasks = [task for task in tasks if task.status == TaskStatus.RUNNING]
    completed_tasks = [task for task in tasks if task.status == TaskStatus.COMPLETED]
    failed_tasks = [task for task in tasks if task.status == TaskStatus.FAILED]

    # 打印任务统计信息
    click.echo(f"总任务数: {len(tasks)}")
    click.echo(f"等待中: {len(waiting_tasks)}")
    click.echo(f"就绪: {len(ready_tasks)}")
    click.echo(f"执行中: {len(running_tasks)}")
    click.echo(f"已完成: {len(completed_tasks)}")
    click.echo(f"失败: {len(failed_tasks)}")
    click.echo("")

    # 打印任务详情
    click.echo("任务详情:")
    click.echo("-" * 120)
    click.echo(f"{'ID':<36} {'名称':<20} {'状态':<10} {'依赖数':<8} {'进程ID':<10} {'创建时间':<20} {'完成时间':<20}")
    click.echo("-" * 120)

    for task in tasks:
        status_color = get_status_color(task.status)
        completed_time = task.completed_at.strftime("%Y-%m-%d %H:%M:%S") if task.completed_at else "-"

        click.echo(
            f"{task.id:<36} {task.name:<20} "
            f"{click.style(task.status.value, fg=status_color):<10} "
            f"{len(task.dependencies):<8} {task.process_id:<10} "
            f"{task.created_at.strftime('%Y-%m-%d %H:%M:%S'):<20} "
            f"{completed_time:<20}"
        )


@cli.command()
@click.option("--file", "-f", required=True, help="任务状态文件路径")
@click.argument("task_id")
def show(file, task_id):
    """显示特定任务的详细信息"""
    storage = TaskStorage(file)
    task = storage.get_task(task_id)

    if task is None:
        click.echo(f"未找到任务: {task_id}")
        return

    # 获取依赖任务
    all_tasks = storage.get_all_tasks()
    task_map = {t.id: t for t in all_tasks}

    # 获取依赖该任务的任务
    dep_graph = build_dependency_graph(all_tasks)
    dependent_tasks = dep_graph.get(task.id, set())

    # 打印任务详情
    click.echo(f"任务ID: {task.id}")
    click.echo(f"名称: {task.name}")
    click.echo(f"状态: {click.style(task.status.value, fg=get_status_color(task.status))}")
    click.echo(f"进程ID: {task.process_id}")
    click.echo(f"创建时间: {task.created_at.strftime('%Y-%m-%d %H:%M:%S')}")

    if task.started_at:
        click.echo(f"开始时间: {task.started_at.strftime('%Y-%m-%d %H:%M:%S')}")

    if task.completed_at:
        click.echo(f"完成时间: {task.completed_at.strftime('%Y-%m-%d %H:%M:%S')}")

        if task.started_at:
            duration = format_time_delta(task.started_at, task.completed_at)
            click.echo(f"执行时长: {duration}")

    if task.error_message:
        click.echo(f"错误信息: {task.error_message}")

    # 打印依赖任务
    click.echo("\n依赖任务:")
    if not task.dependencies:
        click.echo("  无")
    else:
        for dep_id in task.dependencies:
            dep_task = task_map.get(dep_id)
            if dep_task:
                status_str = click.style(dep_task.status.value, fg=get_status_color(dep_task.status))
                click.echo(f"  {dep_id} - {dep_task.name} ({status_str})")
            else:
                click.echo(f"  {dep_id} - 未找到")

    # 打印依赖该任务的任务
    click.echo("\n被依赖:")
    if not dependent_tasks:
        click.echo("  无")
    else:
        for dep_id in dependent_tasks:
            dep_task = task_map.get(dep_id)
            if dep_task:
                status_str = click.style(dep_task.status.value, fg=get_status_color(dep_task.status))
                click.echo(f"  {dep_id} - {dep_task.name} ({status_str})")
            else:
                click.echo(f"  {dep_id} - 未找到")

    # 打印元数据
    if task.metadata:
        click.echo("\n元数据:")
        for key, value in task.metadata.items():
            click.echo(f"  {key}: {value}")


@cli.command()
@click.option("--file", "-f", required=True, help="任务状态文件路径")
def graph(file):
    """显示任务依赖关系图"""
    storage = TaskStorage(file)
    tasks = storage.get_all_tasks()

    if not tasks:
        click.echo("没有找到任务")
        return

    # 获取任务执行顺序
    task_order = get_task_execution_order(tasks)
    task_map = {task.id: task for task in tasks}

    # 构建依赖图
    dep_graph = {}
    for task in tasks:
        deps = []
        for dep_id in task.dependencies:
            if dep_id in task_map:
                deps.append(dep_id)
        dep_graph[task.id] = deps

    # 打印依赖图
    click.echo("任务依赖关系图:")
    click.echo("")

    for task_id in task_order:
        task = task_map.get(task_id)
        if not task:
            continue

        # 打印当前任务
        status_str = click.style(task.status.value, fg=get_status_color(task.status))
        click.echo(f"{task.id} - {task.name} ({status_str})")

        # 打印依赖关系
        for dep_id in dep_graph.get(task.id, []):
            dep_task = task_map.get(dep_id)
            if dep_task:
                click.echo(f"  ↳ 依赖 {dep_id} - {dep_task.name}")

        click.echo("")


@cli.command()
@click.option("--file", "-f", required=True, help="任务状态文件路径")
@click.argument("task_id")
def delete(file, task_id):
    """删除指定任务ID的任务

    如果要删除的任务不是其他任务的依赖，或者依赖这个任务的其他任务都已经被完成，则可以被成功删除，
    否则删除失败，并输出日志。
    """
    storage = TaskStorage(file)

    # 检查任务是否存在
    task = storage.get_task(task_id)
    if task is None:
        click.echo(f"错误: 未找到任务 ID: {task_id}")
        click.echo(f"日志: 删除操作失败 - 任务 {task_id} 不存在")
        return

    # 获取所有任务
    all_tasks = storage.get_all_tasks()

    # 检查是否有其他任务依赖于要删除的任务
    dependent_tasks = []
    for t in all_tasks:
        if task_id in t.dependencies:
            dependent_tasks.append(t)

    # 如果有依赖任务，检查这些依赖任务的状态
    if dependent_tasks:
        # 分离已完成和未完成的依赖任务
        completed_dependent_tasks = [t for t in dependent_tasks if t.status == TaskStatus.COMPLETED]
        incomplete_dependent_tasks = [t for t in dependent_tasks if t.status != TaskStatus.COMPLETED]

        if incomplete_dependent_tasks:
            # 存在未完成的依赖任务，不能删除
            click.echo(f"错误: 无法删除任务 '{task.name}' (ID: {task_id})，因为以下任务依赖于它且尚未完成:")
            for dep_task in incomplete_dependent_tasks:
                status_str = click.style(dep_task.status.value, fg=get_status_color(dep_task.status))
                click.echo(f"  - {dep_task.name} (ID: {dep_task.id}) [{status_str}]")

            if completed_dependent_tasks:
                click.echo("\n以下依赖任务已完成:")
                for dep_task in completed_dependent_tasks:
                    status_str = click.style(dep_task.status.value, fg=get_status_color(dep_task.status))
                    click.echo(f"  - {dep_task.name} (ID: {dep_task.id}) [{status_str}]")

            click.echo("")
            click.echo("请等待所有依赖任务完成后再删除此任务")
            click.echo(
                f"日志: 删除操作失败 - 任务 '{task.name}' (ID: {task_id}) 存在 {len(incomplete_dependent_tasks)} 个未完成的依赖任务"
            )
            return
        else:
            # 所有依赖任务都已完成，可以删除
            click.echo(f"信息: 任务 '{task.name}' (ID: {task_id}) 的所有依赖任务都已完成，可以安全删除")
            for dep_task in completed_dependent_tasks:
                status_str = click.style(dep_task.status.value, fg=get_status_color(dep_task.status))
                click.echo(f"  - {dep_task.name} (ID: {dep_task.id}) [{status_str}]")
            click.echo(
                f"日志: 删除检查通过 - 任务 '{task.name}' (ID: {task_id}) 的 {len(completed_dependent_tasks)} 个依赖任务都已完成"
            )
    else:
        # 没有依赖任务，可以直接删除
        click.echo(f"信息: 任务 '{task.name}' (ID: {task_id}) 没有其他任务依赖，可以安全删除")
        click.echo(f"日志: 删除检查通过 - 任务 '{task.name}' (ID: {task_id}) 没有依赖任务")

    # 确认删除
    status_str = click.style(task.status.value, fg=get_status_color(task.status))
    if click.confirm(f"确定要删除任务 '{task.name}' (ID: {task_id}) [{status_str}] 吗？此操作不可撤销。"):
        success = storage.delete_task(task_id)
        if success:
            click.echo(f"成功删除任务: {task.name} (ID: {task_id})")
            click.echo(f"日志: 删除操作成功 - 任务 '{task.name}' (ID: {task_id}) 已从系统中移除")
        else:
            click.echo(f"删除任务失败: {task.name} (ID: {task_id})")
            click.echo(f"日志: 删除操作失败 - 任务 '{task.name}' (ID: {task_id}) 删除时发生错误")
    else:
        click.echo("操作已取消")
        click.echo(f"日志: 删除操作取消 - 用户取消了删除任务 '{task.name}' (ID: {task_id}) 的操作")


@cli.command()
@click.option("--file", "-f", required=True, help="任务状态文件路径")
@click.option("--force", is_flag=True, help="强制清空所有任务（包括未完成的任务）")
def clear(file, force):
    """清空已完成的任务，或强制清空所有任务"""
    storage = TaskStorage(file)
    tasks = storage.get_all_tasks()

    if not tasks:
        click.echo("没有找到任务")
        return

    # 按状态分组
    completed_tasks = [task for task in tasks if task.status == TaskStatus.COMPLETED]
    non_completed_tasks = [task for task in tasks if task.status != TaskStatus.COMPLETED]

    if force:
        # 强制清空所有任务
        if click.confirm(f"确定要删除所有 {len(tasks)} 个任务吗？此操作不可撤销。"):
            storage.clear_all_tasks()
            click.echo(f"已删除所有 {len(tasks)} 个任务")
        else:
            click.echo("操作已取消")
        return

    # 检查是否有未完成的任务
    if non_completed_tasks:
        click.echo(f"错误: 存在 {len(non_completed_tasks)} 个未完成的任务，无法清空")
        click.echo("未完成的任务:")
        for task in non_completed_tasks:
            status_str = click.style(task.status.value, fg=get_status_color(task.status))
            click.echo(f"  - {task.name} ({status_str})")
        click.echo("")
        click.echo("请等待所有任务完成后再执行清空操作，或使用 --force 参数强制删除所有任务")
        return

    # 只有已完成的任务，可以安全清空
    if completed_tasks:
        if click.confirm(f"确定要删除所有 {len(completed_tasks)} 个已完成的任务吗？"):
            storage.clear_all_tasks()
            click.echo(f"已删除所有 {len(completed_tasks)} 个已完成的任务")
        else:
            click.echo("操作已取消")
    else:
        click.echo("没有已完成的任务需要清空")


@cli.command()
@click.option("--file", "-f", required=True, help="任务状态文件路径")
def watch(file):
    """实时监控任务状态变化"""
    try:
        from watchdog.observers import Observer
        from watchdog.events import FileSystemEventHandler
    except ImportError:
        click.echo("错误: 需要安装 watchdog 库才能使用 watch 功能")
        click.echo("请运行: pip install watchdog")
        return

    class TaskFileHandler(FileSystemEventHandler):
        def on_modified(self, event):
            if event.src_path == os.path.abspath(file):
                os.system("cls" if os.name == "nt" else "clear")
                click.echo(f"任务状态更新 - {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
                click.echo("")

                # 调用list命令显示任务列表
                storage = TaskStorage(file)
                tasks = storage.get_all_tasks()

                if not tasks:
                    click.echo("没有找到任务")
                    return

                # 按状态分组
                waiting_tasks = [task for task in tasks if task.status == TaskStatus.WAITING]
                ready_tasks = [task for task in tasks if task.status == TaskStatus.READY]
                running_tasks = [task for task in tasks if task.status == TaskStatus.RUNNING]
                completed_tasks = [task for task in tasks if task.status == TaskStatus.COMPLETED]
                failed_tasks = [task for task in tasks if task.status == TaskStatus.FAILED]

                # 打印任务统计信息
                click.echo(f"总任务数: {len(tasks)}")
                click.echo(f"等待中: {len(waiting_tasks)}")
                click.echo(f"就绪: {len(ready_tasks)}")
                click.echo(f"执行中: {len(running_tasks)}")
                click.echo(f"已完成: {len(completed_tasks)}")
                click.echo(f"失败: {len(failed_tasks)}")
                click.echo("")

                # 打印任务详情
                click.echo("任务详情:")
                click.echo("-" * 120)
                click.echo(
                    f"{'ID':<36} {'名称':<20} {'状态':<10} {'依赖数':<8} {'进程ID':<10} {'创建时间':<20} {'完成时间':<20}"
                )
                click.echo("-" * 120)

                for task in tasks:
                    status_color = get_status_color(task.status)
                    completed_time = task.completed_at.strftime("%Y-%m-%d %H:%M:%S") if task.completed_at else "-"

                    click.echo(
                        f"{task.id:<36} {task.name:<20} "
                        f"{click.style(task.status.value, fg=status_color):<10} "
                        f"{len(task.dependencies):<8} {task.process_id:<10} "
                        f"{task.created_at.strftime('%Y-%m-%d %H:%M:%S'):<20} "
                        f"{completed_time:<20}"
                    )

    click.echo(f"开始监控任务状态文件: {file}")
    click.echo("按 Ctrl+C 退出")
    click.echo("")

    # 确保文件存在
    if not os.path.exists(file):
        click.echo(f"文件不存在: {file}")
        return

    # 显示初始状态
    os.system("cls" if os.name == "nt" else "clear")
    click.echo(f"任务状态 - {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    click.echo("")
    ctx = click.Context(list)
    ctx.invoke(list, file=file)

    # 设置文件监控
    event_handler = TaskFileHandler()
    observer = Observer()
    observer.schedule(event_handler, path=os.path.dirname(os.path.abspath(file)), recursive=False)
    observer.start()

    try:
        while True:
            time.sleep(1)
    except KeyboardInterrupt:
        observer.stop()

    observer.join()


def get_status_color(status):
    """获取任务状态对应的颜色"""
    return {
        TaskStatus.WAITING: "yellow",
        TaskStatus.READY: "blue",
        TaskStatus.RUNNING: "cyan",
        TaskStatus.COMPLETED: "green",
        TaskStatus.FAILED: "red",
    }.get(status, "white")


def main():
    """命令行入口"""
    cli()


if __name__ == "__main__":
    main()
